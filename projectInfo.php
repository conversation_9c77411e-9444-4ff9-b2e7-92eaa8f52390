<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <link rel="stylesheet" href="./index.css" />
  <title>信息查询</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    h1 {
      text-align: center;
      padding: 20px 0;
    }

    .box-card {
      max-width: 1000px;
      margin: 0 auto;
    }

    .btn {
      display: block;
      margin: 20px auto;
    }

    @media only screen and (max-width: 480px) {
      body {
        padding: 10px;
      }

      .el-col-12 {
        width: 100% !important;
      }
    }
  </style>
  <title>Document</title>
</head>

<body>
  <div id="app">
    <el-card class="box-card" style="margin-bottom: 20px">
      <el-form label-width="160px" :model="form" ref="form" label-suffix=" :">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告编号" prop="code" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.code"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-button type="primary" @click="submitForm">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="box-card" style="margin-bottom: 20px">
      <el-form label-width="160px" :model="info" label-suffix=" :">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告编号">{{info.code}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品名称/工程名称">{{info.projectName}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号规格">{{info.unit}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="委托单位">{{info.company}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测项目">{{info.item}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测类型">{{info.checkingOfType}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测地点">{{info.address}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测日期">{{info.date}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="环境条件-温度">{{info.temperature}}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="环境条件-湿度">{{info.humidity}}</el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="样品状态">{{info.status}}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="检测结论">{{info.conclusion}}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <p v-if='info.projectName' style="text-align: center;padding-top: 40px;color: #666;font-size: 12px;">
        该报告由“浙江正联检测科技有限公司”出具，真实有效。</p>
    </el-card>
  </div>
  <script src="./vue.js"></script>
  <script src="./index.js"></script>
  <script src="./axios.min.js"></script>
  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          info: {
          },
          form: {
            code: '',// 报告编号
          }
        }
      },
      methods: {
        submitForm(formName) {
          this.$refs['form'].validate(valid => {
            if (valid) {
              this.info = {};
              axios({
                method: 'post',
                url: 'query.php',
                data: this.form
              }).then(res => {
                if (res.data.code === 200) {
                  this.info = res.data.data
                } else {
                  this.$message.warning({ message: res.data.message })
                }
              })
            } else {
              console.log('error submit!!')
              return false
            }
          })
        }
      }
    })
  </script>
</body>

</html>