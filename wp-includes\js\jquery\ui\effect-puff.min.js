/*!
 * jQuery UI Effects Puff 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/puff-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect","./effect-scale"],e):e(jQuery)}(function(r){return r.effects.effect.puff=function(e,t){var f=r(this),i=r.effects.setMode(f,e.mode||"hide"),h="hide"===i,d=parseInt(e.percent,10)||150,o=d/100,u={height:f.height(),width:f.width(),outerHeight:f.outerHeight(),outerWidth:f.outerWidth()};r.extend(e,{effect:"scale",queue:!1,fade:!0,mode:i,complete:t,percent:h?d:100,from:h?u:{height:u.height*o,width:u.width*o,outerHeight:u.outerHeight*o,outerWidth:u.outerWidth*o}}),f.effect(e)}});