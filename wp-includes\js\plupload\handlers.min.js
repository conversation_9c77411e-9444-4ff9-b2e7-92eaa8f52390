var uploader,uploader_init,topWin=window.dialogArguments||opener||parent||top;function fileQueued(e){jQuery(".media-blank").remove();var r=jQuery("#media-items").children(),a=post_id||0;1==r.length&&r.removeClass("open").find(".slidetoggle").slideUp(200),jQuery('<div class="media-item">').attr("id","media-item-"+e.id).addClass("child-of-"+a).append('<div class="progress"><div class="percent">0%</div><div class="bar"></div></div>',jQuery('<div class="filename original">').text(" "+e.name)).appendTo(jQuery("#media-items")),jQuery("#insert-gallery").prop("disabled",!0)}function uploadStart(){try{void 0!==topWin.tb_remove&&topWin.jQuery("#TB_overlay").unbind("click",topWin.tb_remove)}catch(e){}return!0}function uploadProgress(e,r){var a=jQuery("#media-item-"+r.id);jQuery(".bar",a).width(200*r.loaded/r.size),jQuery(".percent",a).html(r.percent+"%")}function fileUploading(e,r){var a=104857600;a<parseInt(e.settings.max_file_size,10)&&r.size>a&&setTimeout(function(){r.status<3&&0===r.loaded&&(wpFileError(r,pluploadL10n.big_upload_failed.replace("%1$s",'<a class="uploader-html" href="#">').replace("%2$s","</a>")),e.stop(),e.removeFile(r),e.start())},1e4)}function updateMediaForm(){var e=jQuery("#media-items").children();1==e.length?(e.addClass("open").find(".slidetoggle").show(),jQuery(".insert-gallery").hide()):1<e.length&&(e.removeClass("open"),jQuery(".insert-gallery").show()),0<e.not(".media-blank").length?jQuery(".savebutton").show():jQuery(".savebutton").hide()}function uploadSuccess(e,r){var a=jQuery("#media-item-"+e.id);(r=r.replace(/^<pre>(\d+)<\/pre>$/,"$1")).match(/media-upload-error|error-div/)?a.html(r):(jQuery(".percent",a).html(pluploadL10n.crunching),prepareMediaItem(e,r),updateMediaForm(),post_id&&a.hasClass("child-of-"+post_id)&&jQuery("#attachments-count").text(+jQuery("#attachments-count").text()+1))}function setResize(e){e?window.resize_width&&window.resize_height?uploader.settings.resize={enabled:!0,width:window.resize_width,height:window.resize_height,quality:100}:uploader.settings.multipart_params.image_resize=!0:delete uploader.settings.multipart_params.image_resize}function prepareMediaItem(e,r){var a="undefined"==typeof shortform?1:2,i=jQuery("#media-item-"+e.id);2==a&&2<shortform&&(a=shortform);try{void 0!==topWin.tb_remove&&topWin.jQuery("#TB_overlay").click(topWin.tb_remove)}catch(e){}isNaN(r)||!r?(i.append(r),prepareMediaItemInit(e)):i.load("async-upload.php",{attachment_id:r,fetch:a},function(){prepareMediaItemInit(e),updateMediaForm()})}function prepareMediaItemInit(a){var e=jQuery("#media-item-"+a.id);jQuery(".thumbnail",e).clone().attr("class","pinkynail toggle").prependTo(e),jQuery(".filename.original",e).replaceWith(jQuery(".filename.new",e)),jQuery("a.delete",e).click(function(){return jQuery.ajax({url:ajaxurl,type:"post",success:deleteSuccess,error:deleteError,id:a.id,data:{id:this.id.replace(/[^0-9]/g,""),action:"trash-post",_ajax_nonce:this.href.replace(/^.*wpnonce=/,"")}}),!1}),jQuery("a.undo",e).click(function(){return jQuery.ajax({url:ajaxurl,type:"post",id:a.id,data:{id:this.id.replace(/[^0-9]/g,""),action:"untrash-post",_ajax_nonce:this.href.replace(/^.*wpnonce=/,"")},success:function(){var e,r=jQuery("#media-item-"+a.id);(e=jQuery("#type-of-"+a.id).val())&&jQuery("#"+e+"-counter").text(+jQuery("#"+e+"-counter").text()+1),post_id&&r.hasClass("child-of-"+post_id)&&jQuery("#attachments-count").text(+jQuery("#attachments-count").text()+1),jQuery(".filename .trashnotice",r).remove(),jQuery(".filename .title",r).css("font-weight","normal"),jQuery("a.undo",r).addClass("hidden"),jQuery(".menu_order_input",r).show(),r.css({backgroundColor:"#ceb"}).animate({backgroundColor:"#fff"},{queue:!1,duration:500,complete:function(){jQuery(this).css({backgroundColor:""})}}).removeClass("undo")}}),!1}),jQuery("#media-item-"+a.id+".startopen").removeClass("startopen").addClass("open").find("slidetoggle").fadeIn()}function wpQueueError(e){jQuery("#media-upload-error").show().html('<div class="error"><p>'+e+"</p></div>")}function wpFileError(e,r){itemAjaxError(e.id,r)}function itemAjaxError(e,r){var a=jQuery("#media-item-"+e),i=a.find(".filename").text();a.data("last-err")!=e&&a.html('<div class="error-div"><a class="dismiss" href="#">'+pluploadL10n.dismiss+"</a><strong>"+pluploadL10n.error_uploading.replace("%s",jQuery.trim(i))+"</strong> "+r+"</div>").data("last-err",e)}function deleteSuccess(e){var r;return"-1"==e?itemAjaxError(this.id,"You do not have permission. Has your session expired?"):"0"==e?itemAjaxError(this.id,"Could not be deleted. Has it been deleted already?"):(r=this.id,e=jQuery("#media-item-"+r),(r=jQuery("#type-of-"+r).val())&&jQuery("#"+r+"-counter").text(jQuery("#"+r+"-counter").text()-1),post_id&&e.hasClass("child-of-"+post_id)&&jQuery("#attachments-count").text(jQuery("#attachments-count").text()-1),1==jQuery("form.type-form #media-items").children().length&&0<jQuery(".hidden","#media-items").length&&(jQuery(".toggle").toggle(),jQuery(".slidetoggle").slideUp(200).siblings().removeClass("hidden")),jQuery(".toggle",e).toggle(),jQuery(".slidetoggle",e).slideUp(200).siblings().removeClass("hidden"),e.css({backgroundColor:"#faa"}).animate({backgroundColor:"#f4f4f4"},{queue:!1,duration:500}).addClass("undo"),jQuery(".filename:empty",e).remove(),jQuery(".filename .title",e).css("font-weight","bold"),jQuery(".filename",e).append('<span class="trashnotice"> '+pluploadL10n.deleted+" </span>").siblings("a.toggle").hide(),jQuery(".filename",e).append(jQuery("a.undo",e).removeClass("hidden")),void jQuery(".menu_order_input",e).hide())}function deleteError(){}function uploadComplete(){jQuery("#insert-gallery").prop("disabled",!1)}function switchUploader(e){e?(deleteUserSetting("uploader"),jQuery(".media-upload-form").removeClass("html-uploader"),"object"==typeof uploader&&uploader.refresh()):(setUserSetting("uploader","1"),jQuery(".media-upload-form").addClass("html-uploader"))}function uploadError(e,r,a,i){var t=104857600;switch(r){case plupload.FAILED:wpFileError(e,pluploadL10n.upload_failed);break;case plupload.FILE_EXTENSION_ERROR:wpFileExtensionError(i,e,pluploadL10n.invalid_filetype);break;case plupload.FILE_SIZE_ERROR:uploadSizeError(i,e);break;case plupload.IMAGE_FORMAT_ERROR:wpFileError(e,pluploadL10n.not_an_image);break;case plupload.IMAGE_MEMORY_ERROR:wpFileError(e,pluploadL10n.image_memory_exceeded);break;case plupload.IMAGE_DIMENSIONS_ERROR:wpFileError(e,pluploadL10n.image_dimensions_exceeded);break;case plupload.GENERIC_ERROR:wpQueueError(pluploadL10n.upload_failed);break;case plupload.IO_ERROR:t<parseInt(i.settings.filters.max_file_size,10)&&e.size>t?wpFileError(e,pluploadL10n.big_upload_failed.replace("%1$s",'<a class="uploader-html" href="#">').replace("%2$s","</a>")):wpQueueError(pluploadL10n.io_error);break;case plupload.HTTP_ERROR:wpQueueError(pluploadL10n.http_error);break;case plupload.INIT_ERROR:jQuery(".media-upload-form").addClass("html-uploader");break;case plupload.SECURITY_ERROR:wpQueueError(pluploadL10n.security_error);break;default:wpFileError(e,pluploadL10n.default_error)}}function uploadSizeError(e,r){var a=pluploadL10n.file_exceeds_size_limit.replace("%s",r.name),a=jQuery("<div />").attr({id:"media-item-"+r.id,"class":"media-item error"}).append(jQuery("<p />").text(a));jQuery("#media-items").append(a),e.removeFile(r)}function wpFileExtensionError(e,r,a){jQuery("#media-items").append('<div id="media-item-'+r.id+'" class="media-item error"><p>'+a+"</p></div>"),e.removeFile(r)}jQuery(document).ready(function(d){d(".media-upload-form").bind("click.uploader",function(e){var r,a=d(e.target);a.is('input[type="radio"]')?(r=a.closest("tr")).hasClass("align")?setUserSetting("align",a.val()):r.hasClass("image-size")&&setUserSetting("imgsize",a.val()):a.is("button.button")?(r=(r=e.target.className||"").match(/url([^ '"]+)/))&&r[1]&&(setUserSetting("urlbutton",r[1]),a.siblings(".urlfield").val(a.data("link-url"))):a.is("a.dismiss")?a.parents(".media-item").fadeOut(200,function(){d(this).remove()}):a.is(".upload-flash-bypass a")||a.is("a.uploader-html")?(d("#media-items, p.submit, span.big-file-warning").css("display","none"),switchUploader(0),e.preventDefault()):a.is(".upload-html-bypass a")?(d("#media-items, p.submit, span.big-file-warning").css("display",""),switchUploader(1),e.preventDefault()):a.is("a.describe-toggle-on")?(a.parent().addClass("open"),a.siblings(".slidetoggle").fadeIn(250,function(){var e,r,a=d(window).scrollTop(),i=d(window).height(),t=d(this).offset().top,o=d(this).height();i&&t&&o&&(r=a+i)<(e=t+o)&&(e-r<t-a?window.scrollBy(0,e-r+10):window.scrollBy(0,t-a-40))}),e.preventDefault()):a.is("a.describe-toggle-off")&&(a.siblings(".slidetoggle").fadeOut(250,function(){a.parent().removeClass("open")}),e.preventDefault())}),uploader_init=function(){-1!=navigator.userAgent.indexOf("Trident/")||-1!=navigator.userAgent.indexOf("MSIE ")||"flash"!==plupload.predictRuntime(wpUploaderInit)||wpUploaderInit.required_features&&wpUploaderInit.required_features.hasOwnProperty("send_binary_string")||(wpUploaderInit.required_features=wpUploaderInit.required_features||{},wpUploaderInit.required_features.send_binary_string=!0),uploader=new plupload.Uploader(wpUploaderInit),d("#image_resize").bind("change",function(){var e=d(this).prop("checked");setResize(e),e?setUserSetting("upload_resize","1"):deleteUserSetting("upload_resize")}),uploader.bind("Init",function(e){var r=d("#plupload-upload-ui");setResize(getUserSetting("upload_resize",!1)),e.features.dragdrop&&!d(document.body).hasClass("mobile")?(r.addClass("drag-drop"),d("#drag-drop-area").on("dragover.wp-uploader",function(){r.addClass("drag-over")}).on("dragleave.wp-uploader, drop.wp-uploader",function(){r.removeClass("drag-over")})):(r.removeClass("drag-drop"),d("#drag-drop-area").off(".wp-uploader")),"html4"===e.runtime&&d(".upload-flash-bypass").hide()}),uploader.bind("postinit",function(e){e.refresh()}),uploader.init(),uploader.bind("FilesAdded",function(e,r){d("#media-upload-error").empty(),uploadStart(),plupload.each(r,function(e){fileQueued(e)}),e.refresh(),e.start()}),uploader.bind("UploadFile",function(e,r){fileUploading(e,r)}),uploader.bind("UploadProgress",function(e,r){uploadProgress(e,r)}),uploader.bind("Error",function(e,r){uploadError(r.file,r.code,r.message,e),e.refresh()}),uploader.bind("FileUploaded",function(e,r,a){uploadSuccess(r,a.response)}),uploader.bind("UploadComplete",function(){uploadComplete()})},"object"==typeof wpUploaderInit&&uploader_init()});