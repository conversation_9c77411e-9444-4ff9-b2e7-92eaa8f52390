/*!
 * jQuery UI Effects Clip 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/clip-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(d){return d.effects.effect.clip=function(e,t){var i,f=d(this),o=["position","top","bottom","left","right","height","width"],c="show"===d.effects.setMode(f,e.mode||"hide"),n="vertical"===(e.direction||"vertical"),s=n?"height":"width",r=n?"top":"left",a={};d.effects.save(f,o),f.show(),i=d.effects.createWrapper(f).css({overflow:"hidden"}),i=(n="IMG"===f[0].tagName?i:f)[s](),c&&(n.css(s,0),n.css(r,i/2)),a[s]=c?i:0,a[r]=c?0:i/2,n.animate(a,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){c||f.hide(),d.effects.restore(f,o),d.effects.removeWrapper(f),t()}})}});