<div class="module-section">
	<div class="module-section-inner">
		<div class="page-width">
			<?php 
				$pid = get_option( 'index-fwts' );
				query_posts( array( 'ignore_sticky_posts'=>true, 'p'=>$pid, 'posts_per_page'=>1, 'post_type'=>'page' ) );
				while( have_posts() ): the_post();
			?>
			<div class="module-section-title-wrapper clearfix">
				<div class="module-section-title clearfix module-icon-default">
					<h2 style="padding-left: 10px;"><?php echo get_the_title( $pid ); ?></h2>
				</div>
			</div>
			<div id="a1portalSkin_ctr127366127366_mainArea" class="module-section-content">
				<div class="qhd-content">
					<div class="typo">
						<span style="font-size:16px;"><a href="<?php the_permalink(); ?>"><img class="typo_img imgtoright" src="<?php echo get_option( 'index-fwts-img' ); ?>" style="width: 564px; height: 303px;"/></a></span>
						<div class="typo_text">
							<ol>
							<p>
								<span style="font-size:16px;">
								<?php if( get_option('fwts-txt') ) { ?>
									<?php echo get_option( 'fwts-txt' ); ?>
								<?php }else{ ?>
									<?php echo mb_strimwidth(strip_tags(apply_filters('the_content', $post->post_content)), 0, 800,"……"); ?>
								<?php }?>
								</span>
							</p>
							</ol>
						</div>
					</div>
				</div>
			</div>
			<?php endwhile; wp_reset_query(); ?>
		</div>
	</div>
</div>