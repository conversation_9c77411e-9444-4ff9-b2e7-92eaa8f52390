/*!
 * jQuery UI Effects Highlight 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/highlight-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(i){return i.effects.effect.highlight=function(e,o){var n=i(this),f=["backgroundImage","backgroundColor","opacity"],c=i.effects.setMode(n,e.mode||"show"),t={backgroundColor:n.css("backgroundColor")};"hide"===c&&(t.opacity=0),i.effects.save(n,f),n.show().css({backgroundImage:"none",backgroundColor:e.color||"#ffff99"}).animate(t,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){"hide"===c&&n.hide(),i.effects.restore(n,f),o()}})}});