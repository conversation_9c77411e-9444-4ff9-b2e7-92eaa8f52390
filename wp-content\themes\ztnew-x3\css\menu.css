﻿/*** ESSENTIAL STYLES ***/
.sf-menu, .sf-menu * {margin:0;	padding:0;list-style:none;}
.sf-menu {line-height:1.0;}
.sf-menu ul {position:absolute;	top:-999em;	width:13em; /* left offset of submenus need to match (see below) */}
.sf-menu ul li {width:100%;}
.sf-menu li:hover {visibility:inherit; /* fixes IE7 'sticky bug' */}
.sf-menu li {float:left;position:relative;}
.sf-menu a {display:block;}
.sf-menu li:hover ul,
.sf-menu li.sfHover ul {left:0;top:2.4em;*top:2.2em; /* ie6,7 bug */z-index:99;}
ul.sf-menu li:hover li ul,
ul.sf-menu li.sfHover li ul {top:-999em;}
ul.sf-menu li li:hover ul,
ul.sf-menu li li.sfHover ul {left:15em; /* match ul width */top:0;}
ul.sf-menu li li:hover li ul,
ul.sf-menu li li.sfHover li ul {top:-999em;}
ul.sf-menu li li li:hover ul,
ul.sf-menu li li li.sfHover ul {left:15em; /* match ul width */	top:0;}

/*** DEMO SKIN ***/
.sf-menu a{border-left:1px solid #fff;border-top:1px solid #CFDEFF;padding:.75em 1em;text-decoration:none;}
.sf-menu a, .sf-menu a:visited{color:#13a;}
.sf-menu li {background:#BDD2FF;}
.sf-menu li li {background:#AABDE6;}
.sf-menu li li li {background:#9AAEDB;}
.sf-menu li:hover, .sf-menu li.sfHover, .sf-menu a:focus, .sf-menu a:hover, .sf-menu a:active {background:#CFDEFF;outline:0;}



/*** arrows **/
.sf-menu a.sf-with-ul {	padding-right:1.8em;/*min-width:1px;*/}
.sf-sub-indicator {position:absolute;display:block;	right:.75em;top:1.05em; /* IE6 only */width:10px;height:10px;text-indent:-999em;overflow:hidden;background:url('../Images/superfish-arrows.gif') no-repeat -10px -100px; /* 8-bit indexed alpha png. IE6 gets solid image only */}
a > .sf-sub-indicator { top:.8em;background-position: 0 -100px; /* use translucent arrow for modern browsers*/}
/* apply hovers to modern browsers */
a:focus > .sf-sub-indicator,
a:hover > .sf-sub-indicator,
a:active > .sf-sub-indicator,
li:hover > a > .sf-sub-indicator,
li.sfHover > a > .sf-sub-indicator {background-position: -10px -100px; /* arrow hovers for modern browsers*/}

/* point right for anchors in subs */
.sf-menu ul .sf-sub-indicator { background-position:  -10px 0; }
.sf-menu ul a > .sf-sub-indicator { background-position:  0 0; }
/* apply hovers to modern browsers */
.sf-menu ul a:focus > .sf-sub-indicator,
.sf-menu ul a:hover > .sf-sub-indicator,
.sf-menu ul a:active > .sf-sub-indicator,
.sf-menu ul li:hover > a > .sf-sub-indicator,
.sf-menu ul li.sfHover > a > .sf-sub-indicator {background-position: -10px 0; /* arrow hovers for modern browsers*/}

/*** shadows for all but IE6 ***/
.sf-shadow ul {	background:url('../Images/superfish-shadow.png') no-repeat bottom right; padding:0 8px 9px 0;}
.sf-shadow ul.sf-shadow-off {background:transparent;}





/*** 垂直 adding sf-vertical in addition to sf-menu creates a vertical menu ***/
.sf-vertical, .sf-vertical li {
	width:	10em;
}
/* this lacks ul at the start of the selector, so the styles from the main CSS file override it where needed */
.sf-vertical li:hover ul,
.sf-vertical li.sfHover ul {
	left:	10em; /* match ul width */
	top:	0;
}

/*** alter arrow directions ***/
.sf-vertical .sf-sub-indicator { background-position: -10px 0; } /* IE6 gets solid image only */
.sf-vertical a > .sf-sub-indicator { background-position: 0 0; } /* use translucent arrow for modern browsers*/

/* hover arrow direction for modern browsers*/
.sf-vertical a:focus > .sf-sub-indicator,
.sf-vertical a:hover > .sf-sub-indicator,
.sf-vertical a:active > .sf-sub-indicator,
.sf-vertical li:hover > a > .sf-sub-indicator,
.sf-vertical li.sfHover > a > .sf-sub-indicator {
	background-position: -10px 0; /* arrow hovers for modern browsers*/
}