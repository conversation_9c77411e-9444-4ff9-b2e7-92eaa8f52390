var wpCookies={each:function(e,t,n){var i,r;if(!e)return 0;if(n=n||e,void 0!==e.length){for(i=0,r=e.length;i<r;i++)if(!1===t.call(n,e[i],i,e))return 0}else for(i in e)if(e.hasOwnProperty(i)&&!1===t.call(n,e[i],i,e))return 0;return 1},getHash:function(e){var t,e=this.get(e);return e&&this.each(e.split("&"),function(e){e=e.split("="),(t=t||{})[e[0]]=e[1]}),t},setHash:function(e,t,n,i,r,s){var o="";this.each(t,function(e,t){o+=(o?"&":"")+t+"="+e}),this.set(e,o,n,i,r,s)},get:function(e){var t,n=document.cookie,i=e+"=";if(n){if(-1===(t=n.indexOf("; "+i))){if(0!==(t=n.indexOf(i)))return null}else t+=2;return-1===(e=n.indexOf(";",t))&&(e=n.length),decodeURIComponent(n.substring(t+i.length,e))}},set:function(e,t,n,i,r,s){var o=new Date;n="object"==typeof n&&n.toGMTString?n.toGMTString():parseInt(n,10)?(o.setTime(o.getTime()+1e3*parseInt(n,10)),o.toGMTString()):"",document.cookie=e+"="+encodeURIComponent(t)+(n?"; expires="+n:"")+(i?"; path="+i:"")+(r?"; domain="+r:"")+(s?"; secure":"")},remove:function(e,t,n,i){this.set(e,"",-1e3,t,n,i)}};function getUserSetting(e,t){var n=getAllUserSettings();return n.hasOwnProperty(e)?n[e]:void 0!==t?t:""}function setUserSetting(e,t,n){if("object"!=typeof userSettings)return!1;var i=userSettings.uid,r=wpCookies.getHash("wp-settings-"+i),s=userSettings.url,o=!!userSettings.secure;return e=e.toString().replace(/[^A-Za-z0-9_-]/g,""),t="number"==typeof t?parseInt(t,10):t.toString().replace(/[^A-Za-z0-9_-]/g,""),r=r||{},n?delete r[e]:r[e]=t,wpCookies.setHash("wp-settings-"+i,r,31536e3,s,"",o),wpCookies.set("wp-settings-time-"+i,userSettings.time,31536e3,s,"",o),e}function deleteUserSetting(e){return setUserSetting(e,"",1)}function getAllUserSettings(){return"object"==typeof userSettings&&wpCookies.getHash("wp-settings-"+userSettings.uid)||{}}