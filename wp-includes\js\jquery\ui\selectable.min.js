/*!
 * jQuery UI Selectable 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/selectable/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./core","./mouse","./widget"],e):e(jQuery)}(function(u){return u.widget("ui.selectable",u.ui.mouse,{version:"1.11.4",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var e,t=this;this.element.addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){(e=u(t.options.filter,t.element[0])).addClass("ui-selectee"),e.each(function(){var e=u(this),t=e.offset();u.data(this,"selectable-item",{element:this,$element:e,left:t.left,top:t.top,right:t.left+e.outerWidth(),bottom:t.top+e.outerHeight(),startselected:!1,selected:e.hasClass("ui-selected"),selecting:e.hasClass("ui-selecting"),unselecting:e.hasClass("ui-unselecting")})})},this.refresh(),this.selectees=e.addClass("ui-selectee"),this._mouseInit(),this.helper=u("<div class='ui-selectable-helper'></div>")},_destroy:function(){this.selectees.removeClass("ui-selectee").removeData("selectable-item"),this.element.removeClass("ui-selectable ui-selectable-disabled"),this._mouseDestroy()},_mouseStart:function(s){var l=this,e=this.options;this.opos=[s.pageX,s.pageY],this.options.disabled||(this.selectees=u(e.filter,this.element[0]),this._trigger("start",s),u(e.appendTo).append(this.helper),this.helper.css({left:s.pageX,top:s.pageY,width:0,height:0}),e.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var e=u.data(this,"selectable-item");e.startselected=!0,s.metaKey||s.ctrlKey||(e.$element.removeClass("ui-selected"),e.selected=!1,e.$element.addClass("ui-unselecting"),e.unselecting=!0,l._trigger("unselecting",s,{unselecting:e.element}))}),u(s.target).parents().addBack().each(function(){var e,t=u.data(this,"selectable-item");if(t)return e=!s.metaKey&&!s.ctrlKey||!t.$element.hasClass("ui-selected"),t.$element.removeClass(e?"ui-unselecting":"ui-selected").addClass(e?"ui-selecting":"ui-unselecting"),t.unselecting=!e,t.selecting=e,(t.selected=e)?l._trigger("selecting",s,{selecting:t.element}):l._trigger("unselecting",s,{unselecting:t.element}),!1}))},_mouseDrag:function(s){if(this.dragged=!0,!this.options.disabled){var e,l=this,i=this.options,n=this.opos[0],c=this.opos[1],a=s.pageX,r=s.pageY;return a<n&&(e=a,a=n,n=e),r<c&&(e=r,r=c,c=e),this.helper.css({left:n,top:c,width:a-n,height:r-c}),this.selectees.each(function(){var e=u.data(this,"selectable-item"),t=!1;e&&e.element!==l.element[0]&&("touch"===i.tolerance?t=!(e.left>a||e.right<n||e.top>r||e.bottom<c):"fit"===i.tolerance&&(t=e.left>n&&e.right<a&&e.top>c&&e.bottom<r),t?(e.selected&&(e.$element.removeClass("ui-selected"),e.selected=!1),e.unselecting&&(e.$element.removeClass("ui-unselecting"),e.unselecting=!1),e.selecting||(e.$element.addClass("ui-selecting"),e.selecting=!0,l._trigger("selecting",s,{selecting:e.element}))):(e.selecting&&((s.metaKey||s.ctrlKey)&&e.startselected?(e.$element.removeClass("ui-selecting"),e.selecting=!1,e.$element.addClass("ui-selected"),e.selected=!0):(e.$element.removeClass("ui-selecting"),e.selecting=!1,e.startselected&&(e.$element.addClass("ui-unselecting"),e.unselecting=!0),l._trigger("unselecting",s,{unselecting:e.element}))),e.selected&&(s.metaKey||s.ctrlKey||e.startselected||(e.$element.removeClass("ui-selected"),e.selected=!1,e.$element.addClass("ui-unselecting"),e.unselecting=!0,l._trigger("unselecting",s,{unselecting:e.element})))))}),!1}},_mouseStop:function(t){var s=this;return this.dragged=!1,u(".ui-unselecting",this.element[0]).each(function(){var e=u.data(this,"selectable-item");e.$element.removeClass("ui-unselecting"),e.unselecting=!1,e.startselected=!1,s._trigger("unselected",t,{unselected:e.element})}),u(".ui-selecting",this.element[0]).each(function(){var e=u.data(this,"selectable-item");e.$element.removeClass("ui-selecting").addClass("ui-selected"),e.selecting=!1,e.selected=!0,e.startselected=!0,s._trigger("selected",t,{selected:e.element})}),this._trigger("stop",t),this.helper.remove(),!1}})});