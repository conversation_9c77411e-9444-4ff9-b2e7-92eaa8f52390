/*!
 * jQuery UI CSS Framework 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/category/theming/
 */

/* Layout helpers
----------------------------------*/
.ui-helper-hidden {
	display: none;
}
.ui-helper-hidden-accessible {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}
.ui-helper-reset {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	line-height: 1.3;
	text-decoration: none;
	font-size: 100%;
	list-style: none;
}
.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
	content: "";
	display: table;
	border-collapse: collapse;
}
.ui-helper-clearfix:after {
	clear: both;
}
.ui-helper-clearfix {
	min-height: 0; /* support: IE7 */
}
.ui-helper-zfix {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	position: absolute;
	opacity: 0;
	filter:Alpha(Opacity=0); /* support: IE8 */
}

.ui-front {
	z-index: 100;
}


/* Interaction Cues
----------------------------------*/
.ui-state-disabled {
	cursor: default !important;
}


/* Icons
----------------------------------*/

/* states and images */
.ui-icon {
	display: block;
	text-indent: -99999px;
	overflow: hidden;
	background-repeat: no-repeat;
}


/* Misc visuals
----------------------------------*/

/* Overlays */
.ui-widget-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

/*!
 * jQuery UI Resizable 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */
.ui-resizable {
	position: relative;
}
.ui-resizable-handle {
	position: absolute;
	font-size: 0.1px;
	display: block;
	-ms-touch-action: none;
	touch-action: none;
}
.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
	display: none;
}
.ui-resizable-n {
	cursor: n-resize;
	height: 7px;
	width: 100%;
	top: -5px;
	left: 0;
}
.ui-resizable-s {
	cursor: s-resize;
	height: 7px;
	width: 100%;
	bottom: -5px;
	left: 0;
}
/* rtl:ignore */
.ui-resizable-e {
	cursor: e-resize;
	width: 7px;
	right: -5px;
	top: 0;
	height: 100%;
}
/* rtl:ignore */
.ui-resizable-w {
	cursor: w-resize;
	width: 7px;
	left: -5px;
	top: 0;
	height: 100%;
}
/* rtl:ignore */
.ui-resizable-se {
	cursor: se-resize;
	width: 12px;
	height: 12px;
	right: 1px;
	bottom: 1px;
}
/* rtl:ignore */
.ui-resizable-sw {
	cursor: sw-resize;
	width: 9px;
	height: 9px;
	left: -5px;
	bottom: -5px;
}
/* rtl:ignore */
.ui-resizable-nw {
	cursor: nw-resize;
	width: 9px;
	height: 9px;
	left: -5px;
	top: -5px;
}
/* rtl:ignore */
.ui-resizable-ne {
	cursor: ne-resize;
	width: 9px;
	height: 9px;
	right: -5px;
	top: -5px;
}

/* WP buttons: see buttons.css. */

.ui-button {
	display: inline-block;
	text-decoration: none;
	font-size: 13px;
	line-height: 26px;
	height: 28px;
	margin: 0;
	padding: 0 10px 1px;
	cursor: pointer;
	border-width: 1px;
	border-style: solid;
	-webkit-appearance: none;
	-webkit-border-radius: 3px;
	border-radius: 3px;
	white-space: nowrap;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	color: #555;
	border-color: #cccccc;
	background: #f7f7f7;
	-webkit-box-shadow: 0 1px 0 #cccccc;
	box-shadow: 0 1px 0 #cccccc;
 	vertical-align: top;
}

.ui-button:active,
.ui-button:focus {
	outline: none;
}

/* Remove the dotted border on :focus and the extra padding in Firefox */
.ui-button::-moz-focus-inner {
	border-width: 0;
	border-style: none;
	padding: 0;
}

.ui-button:hover,
.ui-button:focus {
	background: #fafafa;
	border-color: #999;
	color: #23282d;
}

.ui-button:focus {
	border-color: #5b9dd9;
	-webkit-box-shadow: 0 0 3px rgba( 0, 115, 170, .8 );
	box-shadow: 0 0 3px rgba( 0, 115, 170, .8 );
}

.ui-button:active {
	background: #eee;
	border-color: #999;
	-webkit-box-shadow: inset 0 2px 5px -3px rgba( 0, 0, 0, 0.5 );
	box-shadow: inset 0 2px 5px -3px rgba( 0, 0, 0, 0.5 );
	-webkit-transform: translateY(1px);
	-ms-transform: translateY(1px);
	transform: translateY(1px);
}

.ui-button[disabled],
.ui-button:disabled {
	color: #a0a5aa !important;
	border-color: #ddd !important;
	background: #f7f7f7 !important;
	-webkit-box-shadow: none !important;
	box-shadow: none !important;
	text-shadow: 0 1px 0 #fff !important;
	cursor: default;
	-webkit-transform: none !important;
	-ms-transform: none !important;
	transform: none !important;
}

@media screen and ( max-width: 782px ) {

	.ui-button {
		padding: 6px 14px;
		line-height: normal;
		font-size: 14px;
		vertical-align: middle;
		height: auto;
		margin-bottom: 4px;
	}

}

/* WP Theme */

.ui-dialog {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 100102;
	background-color: #fff;
	-webkit-box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
	box-shadow: 0 3px 6px rgba( 0, 0, 0, 0.3 );
}

.ui-dialog-titlebar {
	background: #fcfcfc;
	border-bottom: 1px solid #dfdfdf;
	height: 36px;
	font-size: 18px;
	font-weight: 600;
	line-height: 36px;
	padding: 0 36px 0 16px;
}

.ui-button.ui-dialog-titlebar-close {
	background: none;
	border: none;
	-webkit-box-shadow: none;
	box-shadow: none;
	color: #666;
	cursor: pointer;
	display: block;
	padding: 0;
	position: absolute;
	top: 0;
	right: 0;
	width: 36px;
	height: 36px;
	text-align: center;
}

.ui-dialog-titlebar-close:before {
	font: normal 20px/1 dashicons;
	vertical-align: top;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	line-height: 36px;
	width: 36px;
	height: 36px;
	content: '\f158';
}

.ui-button.ui-dialog-titlebar-close:hover {
	color: #00a0d2;
}

.ui-dialog-titlebar-close .ui-button-text {
	display: none;
}

.ui-dialog-content {
	padding: 16px;
	overflow: auto;
}

.ui-dialog-buttonpane {
	background: #fcfcfc;
	border-top: 1px solid #dfdfdf;
	padding: 16px;
}

.ui-dialog-buttonpane .ui-button {
	margin-left: 16px;
}

.ui-dialog-buttonpane .ui-dialog-buttonset {
	float: right;
}

.ui-draggable .ui-dialog-titlebar {
	cursor: move;
}

.ui-widget-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	min-height: 360px;
	background: #000;
	opacity: 0.7;
	filter: alpha(opacity=70);
	z-index: 100101;
}
