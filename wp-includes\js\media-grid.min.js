!function o(n,s,a){function r(t,e){if(!s[t]){if(!n[t]){var i="function"==typeof require&&require;if(!e&&i)return i(t,!0);if(d)return d(t,!0);throw(i=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",i}i=s[t]={exports:{}},n[t][0].call(i.exports,function(e){return r(n[t][1][e]||e)},i,i.exports,o,n,s,a)}return s[t].exports}for(var d="function"==typeof require&&require,e=0;e<a.length;e++)r(a[e]);return r}({1:[function(e,t,i){var o=wp.media.view.l10n,o=wp.media.controller.State.extend({defaults:{id:"edit-attachment",title:o.attachmentDetails,content:"edit-metadata",menu:!1,toolbar:!1,router:!1}});t.exports=o},{}],2:[function(e,t,i){var o=wp.media;o.controller.EditAttachmentMetadata=e("./controllers/edit-attachment-metadata.js"),o.view.MediaFrame.Manage=e("./views/frame/manage.js"),o.view.Attachment.Details.TwoColumn=e("./views/attachment/details-two-column.js"),o.view.MediaFrame.Manage.Router=e("./routers/manage.js"),o.view.EditImage.Details=e("./views/edit-image-details.js"),o.view.MediaFrame.EditAttachments=e("./views/frame/edit-attachments.js"),o.view.SelectModeToggleButton=e("./views/button/select-mode-toggle.js"),o.view.DeleteSelectedButton=e("./views/button/delete-selected.js"),o.view.DeleteSelectedPermanentlyButton=e("./views/button/delete-selected-permanently.js")},{"./controllers/edit-attachment-metadata.js":1,"./routers/manage.js":3,"./views/attachment/details-two-column.js":4,"./views/button/delete-selected-permanently.js":5,"./views/button/delete-selected.js":6,"./views/button/select-mode-toggle.js":7,"./views/edit-image-details.js":8,"./views/frame/edit-attachments.js":9,"./views/frame/manage.js":10}],3:[function(e,t,i){var o=Backbone.Router.extend({routes:{"upload.php?item=:slug":"showItem","upload.php?search=:query":"search"},baseUrl:function(e){return"upload.php"+e},search:function(e){jQuery("#media-search-input").val(e).trigger("input")},showItem:function(e){var t=wp.media,i=t.frame.state().get("library").findWhere({id:parseInt(e,10)});i?t.frame.trigger("edit:attachment",i):(i=t.attachment(e),t.frame.listenTo(i,"change",function(e){t.frame.stopListening(i),t.frame.trigger("edit:attachment",e)}),i.fetch())}});t.exports=o},{}],4:[function(e,t,i){var o=wp.media.view.Attachment.Details,n=o.extend({template:wp.template("attachment-details-two-column"),editAttachment:function(e){e.preventDefault(),this.controller.content.mode("edit-image")},toggleSelectionHandler:function(){},render:function(){o.prototype.render.apply(this,arguments),wp.media.mixin.removeAllPlayers(),this.$("audio, video").each(function(e,t){t=wp.media.view.MediaDetails.prepareSrc(t);new window.MediaElementPlayer(t,wp.media.mixin.mejsSettings)})}});t.exports=n},{}],5:[function(e,t,i){var o=wp.media.view.Button,n=wp.media.view.DeleteSelectedButton,s=n.extend({initialize:function(){n.prototype.initialize.apply(this,arguments),this.controller.on("select:activate",this.selectActivate,this),this.controller.on("select:deactivate",this.selectDeactivate,this)},filterChange:function(e){this.canShow="trash"===e.get("status")},selectActivate:function(){this.toggleDisabled(),this.$el.toggleClass("hidden",!this.canShow)},selectDeactivate:function(){this.toggleDisabled(),this.$el.addClass("hidden")},render:function(){return o.prototype.render.apply(this,arguments),this.selectActivate(),this}});t.exports=s},{}],6:[function(e,t,i){var o=wp.media.view.Button,n=wp.media.view.l10n,s=o.extend({initialize:function(){o.prototype.initialize.apply(this,arguments),this.options.filters&&this.options.filters.model.on("change",this.filterChange,this),this.controller.on("selection:toggle",this.toggleDisabled,this)},filterChange:function(e){"trash"===e.get("status")?this.model.set("text",n.untrashSelected):wp.media.view.settings.mediaTrash?this.model.set("text",n.trashSelected):this.model.set("text",n.deleteSelected)},toggleDisabled:function(){this.model.set("disabled",!this.controller.state().get("selection").length)},render:function(){return o.prototype.render.apply(this,arguments),this.controller.isModeActive("select")?this.$el.addClass("delete-selected-button"):this.$el.addClass("delete-selected-button hidden"),this.toggleDisabled(),this}});t.exports=s},{}],7:[function(e,t,i){var o=wp.media.view.Button,n=wp.media.view.l10n,s=o.extend({initialize:function(){_.defaults(this.options,{size:""}),o.prototype.initialize.apply(this,arguments),this.controller.on("select:activate select:deactivate",this.toggleBulkEditHandler,this),this.controller.on("selection:action:done",this.back,this)},back:function(){this.controller.deactivateMode("select").activateMode("edit")},click:function(){o.prototype.click.apply(this,arguments),this.controller.isModeActive("select")?this.back():this.controller.deactivateMode("edit").activateMode("select")},render:function(){return o.prototype.render.apply(this,arguments),this.$el.addClass("select-mode-toggle-button"),this},toggleBulkEditHandler:function(){var e=this.controller.content.get().toolbar,t=e.$(".media-toolbar-secondary > *, .media-toolbar-primary > *");this.controller.isModeActive("select")?(this.model.set({size:"large",text:n.cancelSelection}),t.not(".spinner, .media-button").hide(),this.$el.show(),e.$(".delete-selected-button").removeClass("hidden")):(this.model.set({size:"",text:n.bulkSelect}),this.controller.content.get().$el.removeClass("fixed"),e.$el.css("width",""),e.$(".delete-selected-button").addClass("hidden"),t.not(".media-button").show(),this.controller.state().get("selection").reset())}});t.exports=s},{}],8:[function(e,t,i){var o=wp.media.View,n=wp.media.view.EditImage.extend({initialize:function(e){this.editor=window.imageEdit,this.frame=e.frame,this.controller=e.controller,o.prototype.initialize.apply(this,arguments)},back:function(){this.frame.content.mode("edit-metadata")},save:function(){this.model.fetch().done(_.bind(function(){this.frame.content.mode("edit-metadata")},this))}});t.exports=n},{}],9:[function(e,t,i){var o=wp.media.view.Frame,n=wp.media.view.MediaFrame,s=jQuery,n=n.extend({className:"edit-attachment-frame",template:wp.template("edit-attachment-frame"),regions:["title","content"],events:{"click .left":"previousMediaItem","click .right":"nextMediaItem"},initialize:function(){o.prototype.initialize.apply(this,arguments),_.defaults(this.options,{modal:!0,state:"edit-attachment"}),this.controller=this.options.controller,this.gridRouter=this.controller.gridRouter,this.library=this.options.library,this.options.model&&(this.model=this.options.model),this.bindHandlers(),this.createStates(),this.createModal(),this.title.mode("default"),this.toggleNav()},bindHandlers:function(){this.on("title:create:default",this.createTitle,this),this.listenTo(this.model,"change:status destroy",this.close,this),this.on("content:create:edit-metadata",this.editMetadataMode,this),this.on("content:create:edit-image",this.editImageMode,this),this.on("content:render:edit-image",this.editImageModeRender,this),this.on("close",this.detach)},createModal:function(){this.options.modal&&(this.modal=new wp.media.view.Modal({controller:this,title:this.options.title}),this.modal.on("open",_.bind(function(){s("body").on("keydown.media-modal",_.bind(this.keyEvent,this))},this)),this.modal.on("close",_.bind(function(){this.modal.remove(),s("body").off("keydown.media-modal"),s('li.attachment[data-id="'+this.model.get("id")+'"]').focus(),this.resetRoute()},this)),this.modal.content(this),this.modal.open())},createStates:function(){this.states.add([new wp.media.controller.EditAttachmentMetadata({model:this.model})])},editMetadataMode:function(e){e.view=new wp.media.view.Attachment.Details.TwoColumn({controller:this,model:this.model}),e.view.views.set(".attachment-compat",new wp.media.view.AttachmentCompat({controller:this,model:this.model})),this.model&&this.gridRouter.navigate(this.gridRouter.baseUrl("?item="+this.model.id))},editImageMode:function(e){var t=new wp.media.controller.EditImage({model:this.model,frame:this});t._toolbar=function(){},t._router=function(){},t._menu=function(){},e.view=new wp.media.view.EditImage.Details({model:this.model,frame:this,controller:t})},editImageModeRender:function(e){e.on("ready",e.loadEditor)},toggleNav:function(){this.$(".left").toggleClass("disabled",!this.hasPrevious()),this.$(".right").toggleClass("disabled",!this.hasNext())},rerender:function(){"edit-metadata"!==this.content.mode()?this.content.mode("edit-metadata"):this.content.render(),this.toggleNav()},previousMediaItem:function(){this.hasPrevious()?(this.model=this.library.at(this.getCurrentIndex()-1),this.rerender(),this.$(".left").focus()):this.$(".left").blur()},nextMediaItem:function(){this.hasNext()?(this.model=this.library.at(this.getCurrentIndex()+1),this.rerender(),this.$(".right").focus()):this.$(".right").blur()},getCurrentIndex:function(){return this.library.indexOf(this.model)},hasNext:function(){return this.getCurrentIndex()+1<this.library.length},hasPrevious:function(){return-1<this.getCurrentIndex()-1},keyEvent:function(e){("INPUT"!==e.target.nodeName&&"TEXTAREA"!==e.target.nodeName||e.target.readOnly||e.target.disabled)&&(39===e.keyCode&&this.nextMediaItem(),37===e.keyCode&&this.previousMediaItem())},resetRoute:function(){this.gridRouter.navigate(this.gridRouter.baseUrl(""))}});t.exports=n},{}],10:[function(e,t,i){var o=wp.media.view.MediaFrame,n=wp.media.controller.Library,s=Backbone.$,a=o.extend({initialize:function(){_.defaults(this.options,{title:"",modal:!1,selection:[],library:{},multiple:"add",state:"library",uploader:!0,mode:["grid","edit"]}),this.$body=s(document.body),this.$window=s(window),this.$adminBar=s("#wpadminbar"),this.$window.on("scroll resize",_.debounce(_.bind(this.fixPosition,this),15)),s(document).on("click",".page-title-action",_.bind(this.addNewClickHandler,this)),this.$el.addClass("wp-core-ui"),!wp.Uploader.limitExceeded&&wp.Uploader.browser.supported||(this.options.uploader=!1),this.options.uploader&&(this.uploader=new wp.media.view.UploaderWindow({controller:this,uploader:{dropzone:document.body,container:document.body}}).render(),this.uploader.ready(),s("body").append(this.uploader.el),this.options.uploader=!1),this.gridRouter=new wp.media.view.MediaFrame.Manage.Router,o.prototype.initialize.apply(this,arguments),this.$el.appendTo(this.options.container),this.createStates(),this.bindRegionModeHandlers(),this.render(),this.bindSearchHandler()},bindSearchHandler:function(){var e=this.$("#media-search-input"),t=this.options.container.data("search"),i=this.browserView.toolbar.get("search").$el,o=this.$(".view-list"),n=_.debounce(function(e){var t=s(e.currentTarget).val(),e="";t&&(e+="?search="+t),this.gridRouter.navigate(this.gridRouter.baseUrl(e))},1e3);e.on("input",_.bind(n,this)),i.val(t).trigger("input"),this.gridRouter.on("route:search",function(){var e=window.location.href;-1<e.indexOf("mode=")?e=e.replace(/mode=[^&]+/g,"mode=list"):e+=-1<e.indexOf("?")?"&mode=list":"?mode=list",e=e.replace("search=","s="),o.prop("href",e)})},createStates:function(){var e=this.options;this.options.states||this.states.add([new n({library:wp.media.query(e.library),multiple:e.multiple,title:e.title,content:"browse",toolbar:"select",contentUserSetting:!1,filterable:"all",autoSelect:!1})])},bindRegionModeHandlers:function(){this.on("content:create:browse",this.browseContent,this),this.on("edit:attachment",this.openEditAttachmentModal,this),this.on("select:activate",this.bindKeydown,this),this.on("select:deactivate",this.unbindKeydown,this)},handleKeydown:function(e){27===e.which&&(e.preventDefault(),this.deactivateMode("select").activateMode("edit"))},bindKeydown:function(){this.$body.on("keydown.select",_.bind(this.handleKeydown,this))},unbindKeydown:function(){this.$body.off("keydown.select")},fixPosition:function(){var e,t;this.isModeActive("select")&&(t=(e=this.$(".attachments-browser")).find(".media-toolbar"),e.offset().top+16<this.$window.scrollTop()+this.$adminBar.height()?(e.addClass("fixed"),t.css("width",e.width()+"px")):(e.removeClass("fixed"),t.css("width","")))},addNewClickHandler:function(e){e.preventDefault(),this.trigger("toggle:upload:attachment"),this.uploader&&this.uploader.refresh()},openEditAttachmentModal:function(e){wp.media({frame:"edit-attachments",controller:this,library:this.state().get("library"),model:e})},browseContent:function(e){var t=this.state();this.browserView=e.view=new wp.media.view.AttachmentsBrowser({controller:this,collection:t.get("library"),selection:t.get("selection"),model:t,sortable:t.get("sortable"),search:t.get("searchable"),filters:t.get("filterable"),date:t.get("date"),display:t.get("displaySettings"),dragInfo:t.get("dragInfo"),sidebar:"errors",suggestedWidth:t.get("suggestedWidth"),suggestedHeight:t.get("suggestedHeight"),AttachmentView:t.get("AttachmentView"),scrollElement:document}),this.browserView.on("ready",_.bind(this.bindDeferred,this)),this.errors=wp.Uploader.errors,this.errors.on("add remove reset",this.sidebarVisibility,this)},sidebarVisibility:function(){this.browserView.$(".media-sidebar").toggle(!!this.errors.length)},bindDeferred:function(){this.browserView.dfd&&this.browserView.dfd.done(_.bind(this.startHistory,this))},startHistory:function(){window.history&&window.history.pushState&&Backbone.history.start({root:window._wpMediaGridSettings.adminUrl,pushState:!0})}});t.exports=a},{}]},{},[2]);