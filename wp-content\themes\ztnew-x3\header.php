﻿<!DOCTYPE html>
<head id="Head">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<meta name="RESOURCE-TYPE" content="DOCUMENT"/>
<meta name="DISTRIBUTION" content="GLOBAL"/>
<meta name="ROBOTS" content="INDEX, FOLLOW"/>
<meta name="REVISIT-AFTER" content="1 DAYS"/>
<meta name="RATING" content="GENERAL"/>
<meta http-equiv="PAGE-ENTER" content="RevealTrans(Duration=0,Transition=1)"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/qhdcontent.css"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/content.css"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/menu.css"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/jquery.fancybox-1.3.4.css"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/pgwslideshow.css"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/animate.min.css"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo( 'stylesheet_url' ); ?>"/>
<link rel="stylesheet" type="text/css" href="<?php bloginfo('template_directory'); ?>/css/style-red.css"/>
<style>
 html { background:url(<?php bloginfo('template_directory'); ?>/images/bg-rep-03.png); }
</style>
<!--[if lt IE 9]>
<script src="<?php bloginfo('template_directory'); ?>/js/html5.js"></script>
<![endif]--><!--[if IE 6]>
<script type="text/javascript" src="<?php bloginfo('template_directory'); ?>/js/ie7.js"></script>
<script type="text/javascript" src="<?php bloginfo('template_directory'); ?>/js/dd_belatedpng.js"></script>
<script type="text/javascript">
  DD_belatedPNG.fix('.top img, .footer img, .bottom img, .form-btn, .module-icon-default');
 </script>
<![endif]-->
<meta charset="UTF-8">
<meta content="width=device-width, initial-scale=1.0, user-scalable=no" name="viewport">
<title><?php wp_title( '|', true, 'right' ); ?></title>
<?php
	if ( is_singular() && get_option( 'thread_comments' ) ) wp_enqueue_script( 'comment-reply' );
	wp_enqueue_script( 'jquery' );
	wp_head();
?>
<?php if( get_option( 'favicon', '' ) ) { ?>
	<link rel="shortcut icon" href="<?php echo get_option( 'favicon' ); ?>" type="image/x-icon" >
	<?php }else{ ?>
	<link rel="Shortcut Icon" href="<?php bloginfo('template_url');?>/images/icon/favicon.ico" type="image/x-icon" />
<?php }?>
</head>
<!-- 外部样式 -->
<body class="font-zh-CN">
<form>
	<script src="<?php bloginfo('template_directory'); ?>/js/a1portalcore.js" type="text/javascript"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/a1portal.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/jquery-1.7.2.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/superfish.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/jquery.caroufredsel.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/jquery.touchswipe.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/jquery.tools.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/jquery.fancybox-1.3.4.pack.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/pgwslideshow.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/jquery.fixed.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/cloud-zoom.1.0.2.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/device.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/html5media-1.2.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/animate.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/isotope.pkgd.min.js"></script>
	<script src="<?php bloginfo('template_directory'); ?>/js/custom.js"></script>
	<div id="wrapper" class="home-page">
		<header class="top header-v4 desktops-section default-top">
		<div class="top-main">
			<div class="page-width clearfix">
				<div class="logo" skinobjectzone="HtmlLogo_412">
					<a href="<?php bloginfo( 'url' ); ?>">
					<?php if( get_option('logo') ) { ?>
						<img src="<?php echo get_option( 'logo' ); ?>" alt="<?php bloginfo('name'); ?>">
					<?php }else{ ?>
						<img src="<?php bloginfo('template_url');?>/images/logo.png" alt="<?php bloginfo('name'); ?>">
					<?php }?>
					</a>
				</div>
				<div class="top-main-content clearfix">
					<nav class="nav">
					<div class="main-nav clearfix" skinobjectzone="menu_481">
						<ul class="sf-menu">
							<?php if(function_exists('wp_nav_menu')) wp_nav_menu(array('container' => false, 'items_wrap' => '%3$s', 'theme_location' => 'main')); ?>
						</ul>
					</div>
					</nav>
				</div>
			</div>
		</div>
		<!-- E top-main --></header><!-- S touch-top-wrapper -->
		<div class="touch-top mobile-section clearfix">
			<div class="touch-top-wrapper clearfix">
				<div class="touch-logo" skinobjectzone="HtmlLogo_1491">
					<a href="<?php bloginfo( 'url' ); ?>">
					<?php if( get_option('logo') ) { ?>
						<img src="<?php echo get_option( 'logo' ); ?>" alt="<?php bloginfo('name'); ?>">
					<?php }else{ ?>
						<img src="<?php bloginfo('template_url');?>/images/logo.png" alt="<?php bloginfo('name'); ?>">
					<?php }?>
					</a>
				</div>
				<!-- S touch-navigation -->
				<div class="touch-navigation">
					<div class="touch-toggle">
						<ul>
							<li class="touch-toggle-item-last"><a href="javascript:;" class="drawer-menu" data-drawer="drawer-section-menu"><i class="touch-icon-menu"></i><span>导航</span></a></li>
						</ul>
					</div>
				</div>
				<!-- E touch-navigation -->
			</div>
			<!-- S touch-top -->
			<div class="touch-toggle-content touch-top-home">
				<div class="drawer-section drawer-section-menu">
					<div class="touch-menu" skinobjectzone="menu_1968">
						<ul>
							<?php if(function_exists('wp_nav_menu')) wp_nav_menu(array('container' => false, 'items_wrap' => '%3$s', 'theme_location' => 'main')); ?>
						</ul>
					</div>
				</div>
				<script type="text/javascript">
    $(document).ready(function(){
     $(".touch-toggle a").click(function(event){
      var className = $(this).attr("data-drawer");
      if( $("."+className).css('display') == 'none' ){      
       $("."+className).slideDown().siblings(".drawer-section").slideUp();
      }else{
       $(".drawer-section").slideUp(); 
      }
      event.stopPropagation();
     });
     /*$(document).click(function(){
      $(".drawer-section").slideUp();     
     })*/
     $('.touch-menu a').click(function(){     
      if( $(this).next().is('ul') ){
       if( $(this).next('ul').css('display') == 'none' ){
        $(this).next('ul').slideDown();
        $(this).find('i').attr("class","touch-arrow-up");     
       }else{
        $(this).next('ul').slideUp();
        $(this).next('ul').find('ul').slideUp();
        $(this).find('i').attr("class","touch-arrow-down");
       }   
      }
     });
    });
				</script>
			</div>
			<!-- E touch-top -->
		</div>