!function(e){"use strict";e.wp=e.wp||{},wp.api=wp.api||new function(){this.models={},this.collections={},this.views={}},wp.api.versionString=wp.api.versionString||"wp/v2/",!_.isFunction(_.includes)&&_.isFunction(_.contains)&&(_.includes=_.contains)}(window),function(e){"use strict";var t,i;e.wp=e.wp||{},wp.api=wp.api||{},wp.api.utils=wp.api.utils||{},Date.prototype.toISOString||(t=function(e){return i=1===(i=String(e)).length?"0"+i:i},Date.prototype.toISOString=function(){return this.getUTCFullYear()+"-"+t(this.getUTCMonth()+1)+"-"+t(this.getUTCDate())+"T"+t(this.getUTCHours())+":"+t(this.getUTCMinutes())+":"+t(this.getUTCSeconds())+"."+String((this.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"}),wp.api.utils.parseISO8601=function(e){var t,i,s,n,o=0,a=[1,4,5,6,7,10,11];if(i=/^(\d{4}|[+\-]\d{6})(?:-(\d{2})(?:-(\d{2}))?)?(?:T(\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+\-])(\d{2})(?::(\d{2}))?)?)?$/.exec(e)){for(s=0;n=a[s];++s)i[n]=+i[n]||0;i[2]=(+i[2]||1)-1,i[3]=+i[3]||1,"Z"!==i[8]&&void 0!==i[9]&&(o=60*i[10]+i[11],"+"===i[9]&&(o=0-o)),t=Date.UTC(i[1],i[2],i[3],i[4],i[5]+o,i[6],i[7])}else t=Date.parse?Date.parse(e):NaN;return t},wp.api.utils.getRootUrl=function(){return e.location.origin?e.location.origin+"/":e.location.protocol+"/"+e.location.host+"/"},wp.api.utils.capitalize=function(e){return _.isUndefined(e)?e:e.charAt(0).toUpperCase()+e.slice(1)},wp.api.utils.capitalizeAndCamelCaseDashes=function(e){return _.isUndefined(e)?e:(e=wp.api.utils.capitalize(e),wp.api.utils.camelCaseDashes(e))},wp.api.utils.camelCaseDashes=function(e){return e.replace(/-([a-z])/g,function(e){return e[1].toUpperCase()})},wp.api.utils.extractRoutePart=function(e,t,i,s){return t=t||1,i=i||wp.api.versionString,e=(e=0===e.indexOf("/"+i)?e.substr(i.length+1):e).split("/"),s&&(e=e.reverse()),_.isUndefined(e[--t])?"":e[t]},wp.api.utils.extractParentName=function(e){var t=e.lastIndexOf("_id>[\\d]+)/");return t<0?"":((t=(t=e.substr(0,t-1)).split("/")).pop(),t=t.pop())},wp.api.utils.decorateFromRoute=function(e,t){_.each(e,function(e){_.includes(e.methods,"POST")||_.includes(e.methods,"PUT")?_.isEmpty(e.args)||(_.isEmpty(t.prototype.args)?t.prototype.args=e.args:t.prototype.args=_.extend(t.prototype.args,e.args)):_.includes(e.methods,"GET")&&(_.isEmpty(e.args)||(_.isEmpty(t.prototype.options)?t.prototype.options=e.args:t.prototype.options=_.extend(t.prototype.options,e.args)))})},wp.api.utils.addMixinsAndHelpers=function(t,e,i){function s(e,t,i,s,n){var o,a=jQuery.Deferred(),e=e.get("_embedded")||{};return _.isNumber(t)&&0!==t?(o=(o=e[s]?_.findWhere(e[s],{id:t}):o)||{id:t},(o=new wp.api.models[i](o)).get(n)?a.resolve(o):o.fetch({success:function(e){a.resolve(e)},error:function(e,t){a.reject(t)}}),a.promise()):(a.reject(),a)}function n(e,t,i,s){var n="",o="",a=jQuery.Deferred(),r=e.get("id"),e=e.get("_embedded")||{};return _.isNumber(r)&&0!==r?(_.isUndefined(i)||_.isUndefined(e[i])?n={parent:r}:o=_.isUndefined(s)?e[i]:e[i][s],n=new wp.api.collections[t](o,n),_.isUndefined(n.models[0])?n.fetch({success:function(e){p(e,r),a.resolve(e)},error:function(e,t){a.reject(t)}}):(p(n,r),a.resolve(n)),a.promise()):(a.reject(),a)}var o=!1,a=["date","modified","date_gmt","modified_gmt"],r={setDate:function(e,t){t=t||"date";if(_.indexOf(a,t)<0)return!1;this.set(t,e.toISOString())},getDate:function(e){var t=e||"date",e=this.get(t);return!(_.indexOf(a,t)<0||_.isNull(e))&&new Date(wp.api.utils.parseISO8601(e))}},p=function(e,t){_.each(e.models,function(e){e.set("parent_post",t)})},c={getMeta:function(){return n(this,"PostMeta","https://api.w.org/meta")}},l={getRevisions:function(){return n(this,"PostRevisions")}},d={getTags:function(){var e=this.get("tags"),t=new wp.api.collections.Tags;return _.isEmpty(e)?jQuery.Deferred().resolve([]):t.fetch({data:{include:e}})},setTags:function(e){var i,s=this,n=[];if(_.isString(e))return!1;_.isArray(e)?(new wp.api.collections.Tags).fetch({data:{per_page:100},success:function(t){_.each(e,function(e){(i=new wp.api.models.Tag(t.findWhere({slug:e}))).set("parent_post",s.get("id")),n.push(i)}),e=new wp.api.collections.Tags(n),s.setTagsWithCollection(e)}}):this.setTagsWithCollection(e)},setTagsWithCollection:function(e){return this.set("tags",e.pluck("id")),this.save()}},u={getCategories:function(){var e=this.get("categories"),t=new wp.api.collections.Categories;return _.isEmpty(e)?jQuery.Deferred().resolve([]):t.fetch({data:{include:e}})},setCategories:function(e){var i,s=this,n=[];if(_.isString(e))return!1;_.isArray(e)?(new wp.api.collections.Categories).fetch({data:{per_page:100},success:function(t){_.each(e,function(e){(i=new wp.api.models.Category(t.findWhere({slug:e}))).set("parent_post",s.get("id")),n.push(i)}),e=new wp.api.collections.Categories(n),s.setCategoriesWithCollection(e)}}):this.setCategoriesWithCollection(e)},setCategoriesWithCollection:function(e){return this.set("categories",e.pluck("id")),this.save()}},g={getAuthorUser:function(){return s(this,this.get("author"),"User","author","name")}},h={getFeaturedMedia:function(){return s(this,this.get("featured_media"),"Media","wp:featuredmedia","source_url")}};return _.isUndefined(t.prototype.args)?t:(_.each(a,function(e){_.isUndefined(t.prototype.args[e])||(o=!0)}),o&&(t=t.extend(r)),_.isUndefined(t.prototype.args.author)||(t=t.extend(g)),_.isUndefined(t.prototype.args.featured_media)||(t=t.extend(h)),_.isUndefined(t.prototype.args.categories)||(t=t.extend(u)),_.isUndefined(i.collections[e+"Meta"])||(t=t.extend(c)),_.isUndefined(t.prototype.args.tags)||(t=t.extend(d)),t=!_.isUndefined(i.collections[e+"Revisions"])?t.extend(l):t)}}(window),function(){"use strict";var n=window.wpApiSettings||{};wp.api.WPApiBaseModel=Backbone.Model.extend({sync:function(e,t,i){var s;return i=i||{},_.isNull(t.get("date_gmt"))&&t.unset("date_gmt"),_.isEmpty(t.get("slug"))&&t.unset("slug"),_.isUndefined(n.nonce)||_.isNull(n.nonce)||(s=i.beforeSend,i.beforeSend=function(e){if(e.setRequestHeader("X-WP-Nonce",n.nonce),s)return s.apply(this,arguments)}),this.requireForceForDelete&&"delete"===e&&(t.url=t.url()+"?force=true"),Backbone.sync(e,t,i)},save:function(e,t){return!(!_.includes(this.methods,"PUT")&&!_.includes(this.methods,"POST"))&&Backbone.Model.prototype.save.call(this,e,t)},destroy:function(e){return!!_.includes(this.methods,"DELETE")&&Backbone.Model.prototype.destroy.call(this,e)}}),wp.api.models.Schema=wp.api.WPApiBaseModel.extend({defaults:{_links:{},namespace:null,routes:{}},initialize:function(e,t){t=t||{},wp.api.WPApiBaseModel.prototype.initialize.call(this,e,t),this.apiRoot=t.apiRoot||n.root,this.versionString=t.versionString||n.versionString},url:function(){return this.apiRoot+this.versionString}})}(),function(){"use strict";var a=window.wpApiSettings||{};wp.api.WPApiBaseCollection=Backbone.Collection.extend({initialize:function(e,t){this.state={data:{},currentPage:null,totalPages:null,totalObjects:null},_.isUndefined(t)?this.parent="":this.parent=t.parent},sync:function(e,t,i){var s,n=this,o=(i=i||{}).beforeSend;return void 0!==a.nonce&&(i.beforeSend=function(e){if(e.setRequestHeader("X-WP-Nonce",a.nonce),o)return o.apply(n,arguments)}),"read"===e&&(i.data?(n.state.data=_.clone(i.data),delete n.state.data.page):n.state.data=i.data={},void 0===i.data.page?(n.state.currentPage=null,n.state.totalPages=null,n.state.totalObjects=null):n.state.currentPage=i.data.page-1,s=i.success,i.success=function(e,t,i){if(_.isUndefined(i)||(n.state.totalPages=parseInt(i.getResponseHeader("x-wp-totalpages"),10),n.state.totalObjects=parseInt(i.getResponseHeader("x-wp-total"),10)),null===n.state.currentPage?n.state.currentPage=1:n.state.currentPage++,s)return s.apply(this,arguments)}),Backbone.sync(e,t,i)},more:function(e){if((e=e||{}).data=e.data||{},_.extend(e.data,this.state.data),void 0===e.data.page){if(!this.hasMore())return!1;null===this.state.currentPage||this.state.currentPage<=1?e.data.page=2:e.data.page=this.state.currentPage+1}return this.fetch(e)},hasMore:function(){return null===this.state.totalPages||null===this.state.totalObjects||null===this.state.currentPage?null:this.state.currentPage<this.state.totalPages}})}(),function(){"use strict";var n,o={},p=window.wpApiSettings||{};window.wp=window.wp||{},wp.api=wp.api||{},_.isEmpty(p)&&(p.root=window.location.origin+"/wp-json/"),n=Backbone.Model.extend({defaults:{apiRoot:p.root,versionString:wp.api.versionString,schema:null,models:{},collections:{}},initialize:function(){var e,t=this;Backbone.Model.prototype.initialize.apply(t,arguments),e=jQuery.Deferred(),t.schemaConstructed=e.promise(),t.schemaModel=new wp.api.models.Schema(null,{apiRoot:t.get("apiRoot"),versionString:t.get("versionString")}),t.schemaModel.once("change",function(){t.constructFromSchema(),e.resolve(t)}),t.get("schema")?t.schemaModel.set(t.schemaModel.parse(t.get("schema"))):!_.isUndefined(sessionStorage)&&(_.isUndefined(p.cacheSchema)||p.cacheSchema)&&sessionStorage.getItem("wp-api-schema-model"+t.get("apiRoot")+t.get("versionString"))?t.schemaModel.set(t.schemaModel.parse(JSON.parse(sessionStorage.getItem("wp-api-schema-model"+t.get("apiRoot")+t.get("versionString"))))):t.schemaModel.fetch({success:function(e){if(!_.isUndefined(sessionStorage)&&(_.isUndefined(p.cacheSchema)||p.cacheSchema))try{sessionStorage.setItem("wp-api-schema-model"+t.get("apiRoot")+t.get("versionString"),JSON.stringify(e))}catch(e){}},error:function(e){window.console.log(e)}})},constructFromSchema:function(){var o=this,a=p.mapping||{models:{Categories:"Category",Comments:"Comment",Pages:"Page",PagesMeta:"PageMeta",PagesRevisions:"PageRevision",Posts:"Post",PostsCategories:"PostCategory",PostsRevisions:"PostRevision",PostsTags:"PostTag",Schema:"Schema",Statuses:"Status",Tags:"Tag",Taxonomies:"Taxonomy",Types:"Type",Users:"User"},collections:{PagesMeta:"PageMeta",PagesRevisions:"PageRevisions",PostsCategories:"PostCategories",PostsMeta:"PostMeta",PostsRevisions:"PostRevisions",PostsTags:"PostTags"}},i=[],s=[],n=o.get("apiRoot").replace(wp.api.utils.getRootUrl(),""),r={models:{},collections:{}};_.each(o.schemaModel.get("routes"),function(e,t){t!==o.get(" versionString")&&t!==n&&t!=="/"+o.get("versionString").slice(0,-1)&&(/(?:.*[+)]|\/me)$/.test(t)?i:s).push({index:t,route:e})}),_.each(i,function(e){var t,i=wp.api.utils.extractRoutePart(e.index,2,o.get("versionString"),!0),s=wp.api.utils.extractRoutePart(e.index,1,o.get("versionString"),!1),n=wp.api.utils.extractRoutePart(e.index,1,o.get("versionString"),!0);s===o.get("versionString")&&(s=""),"me"===n&&(i="me"),""!==s&&s!==i?(t=wp.api.utils.capitalizeAndCamelCaseDashes(s)+wp.api.utils.capitalizeAndCamelCaseDashes(i),t=a.models[t]||t,r.models[t]=wp.api.WPApiBaseModel.extend({url:function(){var e=o.get("apiRoot")+o.get("versionString")+s+"/"+(_.isUndefined(this.get("parent"))||0===this.get("parent")?_.isUndefined(this.get("parent_post"))?"":this.get("parent_post")+"/":this.get("parent")+"/")+i;return _.isUndefined(this.get("id"))||(e+="/"+this.get("id")),e},route:e,name:t,methods:e.route.methods,initialize:function(e,t){wp.api.WPApiBaseModel.prototype.initialize.call(this,e,t),"Posts"!==this.name&&"Pages"!==this.name&&_.includes(this.methods,"DELETE")&&(this.requireForceForDelete=!0)}})):(t=wp.api.utils.capitalizeAndCamelCaseDashes(i),t=a.models[t]||t,r.models[t]=wp.api.WPApiBaseModel.extend({url:function(){var e=o.get("apiRoot")+o.get("versionString")+("me"===i?"users/me":i);return _.isUndefined(this.get("id"))||(e+="/"+this.get("id")),e},route:e,name:t,methods:e.route.methods})),wp.api.utils.decorateFromRoute(e.route.endpoints,r.models[t],o.get("versionString"))}),_.each(s,function(e){var t,i,s=e.index.slice(e.index.lastIndexOf("/")+1),n=wp.api.utils.extractRoutePart(e.index,1,o.get("versionString"),!1);""!==n&&n!==s&&o.get("versionString")!==n?(t=wp.api.utils.capitalizeAndCamelCaseDashes(n)+wp.api.utils.capitalizeAndCamelCaseDashes(s),i=a.models[t]||t,t=a.collections[t]||t,r.collections[t]=wp.api.WPApiBaseCollection.extend({url:function(){return o.get("apiRoot")+o.get("versionString")+n+"/"+this.parent+"/"+s},model:function(e,t){return new r.models[i](e,t)},name:t,route:e,methods:e.route.methods})):(t=wp.api.utils.capitalizeAndCamelCaseDashes(s),i=a.models[t]||t,t=a.collections[t]||t,r.collections[t]=wp.api.WPApiBaseCollection.extend({url:function(){return o.get("apiRoot")+o.get("versionString")+s},model:function(e,t){return new r.models[i](e,t)},name:t,route:e,methods:e.route.methods})),wp.api.utils.decorateFromRoute(e.route.endpoints,r.collections[t])}),_.each(r.models,function(e,t){r.models[t]=wp.api.utils.addMixinsAndHelpers(e,t,r)}),o.set("models",r.models),o.set("collections",r.collections)}}),wp.api.endpoints=new Backbone.Collection,wp.api.init=function(e){var t,i,s={};return e=e||{},s.apiRoot=e.apiRoot||p.root||"/wp-json",s.versionString=e.versionString||p.versionString||"wp/v2/",s.schema=e.schema||null,s.schema||s.apiRoot!==p.root||s.versionString!==p.versionString||(s.schema=p.schema),o[s.apiRoot+s.versionString]||(t=(t=wp.api.endpoints.findWhere({apiRoot:s.apiRoot,versionString:s.versionString}))||new n(s),e=(i=jQuery.Deferred()).promise(),t.schemaConstructed.done(function(e){wp.api.endpoints.add(e),wp.api.models=_.extend(wp.api.models,e.get("models")),wp.api.collections=_.extend(wp.api.collections,e.get("collections")),i.resolve(e)}),o[s.apiRoot+s.versionString]=e),o[s.apiRoot+s.versionString]},wp.api.loadPromise=wp.api.init()}();