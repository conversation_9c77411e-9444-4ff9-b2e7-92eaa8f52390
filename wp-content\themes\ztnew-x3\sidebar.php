<div id="float">
<section class="sidebar float-left">
<section>
<div class="page-menu-content">
	<ul>
	<?php if ( is_page() ) { ?>
    <!-- SubPageList begin -->
    <?php
                $parent_page = $post->ID;
                while($parent_page) {
                    $page_query = $wpdb->get_row("SELECT ID, post_title, post_status, post_parent FROM $wpdb->posts WHERE ID = '$parent_page'");
                    $parent_page = $page_query->post_parent;
                 }
               $parent_id = $page_query->ID;
               $parent_title = $page_query->post_title;
                if ($wpdb->get_results("SELECT * FROM $wpdb->posts WHERE post_parent = '$parent_id' AND post_status != 'attachment'")) { ?>
    <?php $subpage = wp_list_pages('depth=1&echo=0&child_of='.$parent_id); ?>
    <?php //key ?>
    <?php if($subpage) { ?>
	<div class="page-menu-title">
	<div class="page-menu-item">
		<h3><?php echo $parent_title; ?></h3>
	</div>
	</div>
    <?php wp_list_pages('depth=1&sort_column=menu_order&title_li=&child_of='. $parent_id); ?>
    <?php } else { ?>
	<div class="page-menu-title">
	<div class="page-menu-item">
		<h3><?php echo $parent_title; ?></h3>
	</div>
	</div>
	<li><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></li>
	<?php } ?>
    <?php } ?>
    <!-- SubPageList end -->
    <?php } elseif( is_search() || is_404() ) { ?>
    <?php } else { ?>
    <?php
				$this_category = get_the_category();
				$category_id = $this_category[0]->cat_ID;
				$parent_id = get_category_root_id( $category_id );
				$category_link = get_category_link( $parent_id );
        		$childcat = get_categories('child_of='.$parent_id);
				if( $childcat && $parent_id ){
			?>
    <!-- SubCatList begin -->
	<div class="page-menu-title">
	<div class="page-menu-item">
		<h3><a href="<?php echo get_category_link( $parent_id ); ?>" title=" <?php echo get_cat_name( $parent_id ); ?>" style="color: #FFF;"><?php echo get_cat_name( $parent_id ); ?></a></h3>
	</div>
	</div>
        <?php wp_list_cats("orderby=id&child_of=" . $parent_id . "&depth=2&hide_empty=0"); ?>
    <!-- SubCatList end -->
    <?php } else { ?>
	<div class="page-menu-title">
	<div class="page-menu-item">
		<h3><?php $category = get_the_category(); echo $category[0]->cat_name; ?></h3>
	</div>
	</div>
    <li><?php the_category(', ') ?></li>
	<?php } ?>
    <?php } ?>
    <?php wp_reset_query(); ?>
	</ul>
</div>
</div>
</section>
</section>
<script type="text/javascript">
function buffer(a,b,c){var d;return function(){if(d)return;d=setTimeout(function(){a.call(this),d=undefined},b)}}(function(){function e(){var d=document.body.scrollTop||document.documentElement.scrollTop;d>b?(a.className="div1 div2",c&&(a.style.top=d-b+"px")):a.className="div1"}var a=document.getElementById("float");if(a==undefined)return!1;var b=0,c,d=a;while(d)b+=d.offsetTop,d=d.offsetParent;c=window.ActiveXObject&&!window.XMLHttpRequest;if(!c||!0)window.onscroll=buffer(e,150,this)})();
</script>