!function(E){E.ui.FloatPanel.zIndex=100100,E.PluginManager.add("wordpress",function(p){var i,t,P=E.DOM,m=E.each,u=p.editorManager.i18n.translate,s=window.jQuery,o=window.wp,a=o&&o.editor&&o.editor.autop&&p.getParam("wpautop",!0);function e(n){var e,o=0,t="hide"===n;!(e=p.theme.panel?p.theme.panel.find(".toolbar:not(.menubar)"):e)||e.length<2||"hide"===n&&!e[1].visible()||(!n&&e[1].visible()&&(n="hide"),m(e,function(e,t){0<t&&("hide"===n?(e.hide(),o+=30):(e.show(),o-=30))}),o&&!t&&(E.Env.iOS||(t=p.getContentAreaContainer().first<PERSON><PERSON><PERSON>,P.setStyle(t,"height",t.clientHeight+o)),"hide"===n?(setUserSetting("hidetb","0"),i&&i.active(!1)):(setUserSetting("hidetb","1"),i&&i.active(!0))),p.fire("wp-toolbar-toggle"))}function n(){}return s&&s(document).triggerHandler("tinymce-editor-setup",[p]),p.addButton("wp_adv",{tooltip:"Toolbar Toggle",cmd:"WP_Adv",onPostRender:function(){(i=this).active("1"===getUserSetting("hidetb"))}}),p.on("PostRender",function(){p.getParam("wordpress_adv_hidden",!0)&&"0"===getUserSetting("hidetb","0")&&e("hide")}),p.addCommand("WP_Adv",function(){e()}),p.on("focus",function(){window.wpActiveEditor=p.id}),p.on("BeforeSetContent",function(e){var n;e.content&&(-1!==e.content.indexOf("\x3c!--more")&&(n=u("Read more..."),e.content=e.content.replace(/<!--more(.*?)-->/g,function(e,t){return'<img src="'+E.Env.transparentSrc+'" data-wp-more="more" data-wp-more-text="'+t+'" class="wp-more-tag mce-wp-more" alt="" title="'+n+'" data-mce-resize="false" data-mce-placeholder="1" />'})),-1!==e.content.indexOf("\x3c!--nextpage--\x3e")&&(n=u("Page break"),e.content=e.content.replace(/<!--nextpage-->/g,'<img src="'+E.Env.transparentSrc+'" data-wp-more="nextpage" class="wp-more-tag mce-wp-nextpage" alt="" title="'+n+'" data-mce-resize="false" data-mce-placeholder="1" />')),e.load&&"raw"!==e.format&&a&&(e.content=o.editor.autop(e.content)),-1===e.content.indexOf("<script")&&-1===e.content.indexOf("<style")||(e.content=e.content.replace(/<(script|style)[^>]*>[\s\S]*?<\/\1>/g,function(e,t){return'<img src="'+E.Env.transparentSrc+'" data-wp-preserve="'+encodeURIComponent(e)+'" data-mce-resize="false" data-mce-placeholder="1" class="mce-object" width="20" height="20" alt="&lt;'+t+'&gt;" title="&lt;'+t+'&gt;" />'})),e.content=e.content.replace(/<p>([^<>]+)<\/p>/gi,function(e,t){return"&nbsp;"!==t&&/\S/.test(t)?e:"<p><br /></p>"}))}),p.on("PostProcess",function(e){e.get&&(e.content=e.content.replace(/<img[^>]+>/g,function(e){var t,n,o="";return-1!==e.indexOf('data-wp-more="more"')?n="\x3c!--more"+(o=(t=e.match(/data-wp-more-text="([^"]+)"/))?t[1]:o)+"--\x3e":-1!==e.indexOf('data-wp-more="nextpage"')?n="\x3c!--nextpage--\x3e":-1!==e.indexOf("data-wp-preserve")&&(t=e.match(/ data-wp-preserve="([^"]+)"/))&&(n=decodeURIComponent(t[1])),n||e}))}),p.on("ResolveName",function(e){var t;"IMG"===e.target.nodeName&&(t=p.dom.getAttrib(e.target,"data-wp-more"))&&(e.name=t)}),p.addCommand("WP_More",function(e){var t,n="wp-more-tag",o=p.dom,i=p.selection.getNode();n+=" mce-wp-"+(e=e||"more"),t=u("more"===e?"Read more...":"Next page"),e='<img src="'+E.Env.transparentSrc+'" alt="" title="'+t+'" class="'+n+'" data-wp-more="'+e+'" data-mce-resize="false" data-mce-placeholder="1" />',"BODY"===i.nodeName||"P"===i.nodeName&&"BODY"===i.parentNode.nodeName?p.insertContent(e):(i=o.getParent(i,function(e){return!(!e.parentNode||"BODY"!==e.parentNode.nodeName)},p.getBody()))&&("P"===i.nodeName?i.appendChild(o.create("p",null,e).firstChild):o.insertAfter(o.create("p",null,e),i),p.nodeChanged())}),p.addCommand("WP_Code",function(){p.formatter.toggle("code")}),p.addCommand("WP_Page",function(){p.execCommand("WP_More","nextpage")}),p.addCommand("WP_Help",function(){var e,t=E.Env.mac?u("Ctrl + Alt + letter:"):u("Shift + Alt + letter:"),n=E.Env.mac?u("Cmd + letter:"):u("Ctrl + letter:"),o=[],i=[],a={},r={},s=0,d=0,l=p.settings.wp_shortcut_labels;function c(e,t){var n="<tr>",o=0;for(t=t||1,m(e,function(e,t){n+="<td><kbd>"+t+"</kbd></td><td>"+u(e)+"</td>",o++});o<t;)n+="<td></td><td></td>",o++;return n+"</tr>"}l&&(m(l,function(e,t){var n;-1!==e.indexOf("meta")?(s++,(n=e.replace("meta","").toLowerCase())&&(a[n]=t,s%2==0&&(o.push(c(a,2)),a={}))):-1!==e.indexOf("access")&&(d++,(n=e.replace("access","").toLowerCase())&&(r[n]=t,d%2==0&&(i.push(c(r,2)),r={})))}),0<s%2&&o.push(c(a,2)),0<d%2&&i.push(c(r,2)),e="<tr><th>"+(e=[u("Letter"),u("Action"),u("Letter"),u("Action")]).join("</th><th>")+"</th></tr>",l=(l='<div class="wp-editor-help">')+"<h2>"+u("Default shortcuts,")+" "+n+'</h2><table class="wp-help-th-center fixed">'+e+o.join("")+"</table><h2>"+u("Additional shortcuts,")+" "+t+'</h2><table class="wp-help-th-center fixed">'+e+i.join("")+"</table>",l=(l=p.plugins.wptextpattern&&(!E.Env.ie||8<E.Env.ie)?(l=l+"<h2>"+u("When starting a new paragraph with one of these formatting shortcuts followed by a space, the formatting will be applied automatically. Press Backspace or Escape to undo.")+'</h2><table class="wp-help-th-center fixed">'+c({"*":"Bullet list","1.":"Numbered list"})+c({"-":"Bullet list","1)":"Numbered list"})+"</table>")+"<h2>"+u("The following formatting shortcuts are replaced when pressing Enter. Press Escape or the Undo button to undo.")+'</h2><table class="wp-help-single">'+c({">":"Blockquote"})+c({"##":"Heading 2"})+c({"###":"Heading 3"})+c({"####":"Heading 4"})+c({"#####":"Heading 5"})+c({"######":"Heading 6"})+c({"---":"Horizontal line"})+"</table>":l)+"<h2>"+u("Focus shortcuts:")+'</h2><table class="wp-help-single">'+c({"Alt + F8":"Inline toolbar (when an image, link or preview is selected)"})+c({"Alt + F9":"Editor menu (when enabled)"})+c({"Alt + F10":"Editor toolbar"})+c({"Alt + F11":"Elements path"})+"</table><p>"+u("To move focus to other buttons use Tab or the arrow keys. To return focus to the editor press Escape or use one of the buttons.")+"</p>",l+="</div>",(l=p.windowManager.open({title:"Keyboard Shortcuts",items:{type:"container",classes:"wp-help",html:l},buttons:{text:"Close",onclick:"close"}})).$el&&(l.$el.find('div[role="application"]').attr("role","document"),(l=l.$el.find(".mce-wp-help"))[0]&&(l.attr("tabindex","0"),l[0].focus(),l.on("keydown",function(e){33<=e.keyCode&&e.keyCode<=40&&e.stopPropagation()}))))}),p.addCommand("WP_Medialib",function(){o&&o.media&&o.media.editor&&o.media.editor.open(p.id)}),p.addButton("wp_more",{tooltip:"Insert Read More tag",onclick:function(){p.execCommand("WP_More","more")}}),p.addButton("wp_page",{tooltip:"Page break",onclick:function(){p.execCommand("WP_More","nextpage")}}),p.addButton("wp_help",{tooltip:"Keyboard Shortcuts",cmd:"WP_Help"}),p.addButton("wp_code",{tooltip:"Code",cmd:"WP_Code",stateSelector:"code"}),o&&o.media&&o.media.editor&&p.addMenuItem("add_media",{text:"Add Media",icon:"wp-media-library",context:"insert",cmd:"WP_Medialib"}),p.addMenuItem("wp_more",{text:"Insert Read More tag",icon:"wp_more",context:"insert",onclick:function(){p.execCommand("WP_More","more")}}),p.addMenuItem("wp_page",{text:"Page break",icon:"wp_page",context:"insert",onclick:function(){p.execCommand("WP_More","nextpage")}}),p.on("BeforeExecCommand",function(e){!E.Env.webkit||"InsertUnorderedList"!==e.command&&"InsertOrderedList"!==e.command||(t=t||p.dom.create("style",{type:"text/css"},"#tinymce,#tinymce span,#tinymce li,#tinymce li>span,#tinymce p,#tinymce p>span{font:medium sans-serif;color:#000;line-height:normal;}"),p.getDoc().head.appendChild(t))}),p.on("ExecCommand",function(e){E.Env.webkit&&t&&("InsertUnorderedList"===e.command||"InsertOrderedList"===e.command)&&p.dom.remove(t)}),p.on("init",function(){var n,o,i,e=E.Env,t=["mceContentBody"],a=p.getDoc(),r=p.dom;e.iOS&&r.addClass(a.documentElement,"ios"),"rtl"===p.getParam("directionality")&&(t.push("rtl"),r.setAttrib(a.documentElement,"dir","rtl")),r.setAttrib(a.documentElement,"lang",p.getParam("wp_lang_attr")),e.ie?9===parseInt(e.ie,10)?t.push("ie9"):8===parseInt(e.ie,10)?t.push("ie8"):e.ie<8&&t.push("ie7"):e.webkit&&t.push("webkit"),t.push("wp-editor"),m(t,function(e){e&&r.addClass(a.body,e)}),p.on("BeforeSetContent",function(e){e.content&&(e.content=e.content.replace(/<p>\s*<(p|div|ul|ol|dl|table|blockquote|h[1-6]|fieldset|pre)( [^>]*)?>/gi,"<$1$2>").replace(/<\/(p|div|ul|ol|dl|table|blockquote|h[1-6]|fieldset|pre)>\s*<\/p>/gi,"</$1>"))}),s&&s(document).triggerHandler("tinymce-editor-init",[p]),window.tinyMCEPreInit&&window.tinyMCEPreInit.dragDropUpload&&r.bind(a,"dragstart dragend dragover drop",function(e){s&&s(document).trigger(new s.Event(e))}),p.getParam("wp_paste_filters",!0)&&(p.on("PastePreProcess",function(e){e.content=e.content.replace(/<br class="?Apple-interchange-newline"?>/gi,""),E.Env.webkit||(e.content=e.content.replace(/(<[^>]+) style="[^"]*"([^>]*>)/gi,"$1$2"),e.content=e.content.replace(/(<[^>]+) data-mce-style=([^>]+>)/gi,"$1 style=$2"))}),p.on("PastePostProcess",function(e){m(r.select("p",e.node),function(e){r.isEmpty(e)&&r.remove(e)})})),p.settings.wp_shortcut_labels&&p.theme.panel&&(n={},o="Shift+Alt+",i="Ctrl+",E.Env.mac&&(o="\u2303\u2325",i="\u2318"),m(p.settings.wp_shortcut_labels,function(e,t){n[t]=e.replace("access",o).replace("meta",i)}),m(p.theme.panel.find("button"),function(e){e&&e.settings.tooltip&&n.hasOwnProperty(e.settings.tooltip)&&(e.settings.tooltip=p.translate(e.settings.tooltip)+" ("+n[e.settings.tooltip]+")")}),m(p.theme.panel.find("listbox"),function(e){e&&"Paragraph"===e.settings.text&&m(e.settings.values,function(e){e.text&&n.hasOwnProperty(e.text)&&(e.shortcut="("+n[e.text]+")")})}))}),p.on("SaveContent",function(e){p.inline||!p.isHidden()?(e.content=e.content.replace(/<p>(?:<br ?\/?>|\u00a0|\uFEFF| )*<\/p>/g,"<p>&nbsp;</p>"),a&&(e.content=o.editor.removep(e.content))):e.content=e.element.value}),p.on("preInit",function(){p.schema.addValidElements("@[id|accesskey|class|dir|lang|style|tabindex|title|contenteditable|draggable|dropzone|hidden|spellcheck|translate],i,b,script[src|async|defer|type|charset|crossorigin|integrity]"),E.Env.iOS&&(p.settings.height=300),m({c:"JustifyCenter",r:"JustifyRight",l:"JustifyLeft",j:"JustifyFull",q:"mceBlockQuote",u:"InsertUnorderedList",o:"InsertOrderedList",m:"WP_Medialib",z:"WP_Adv",t:"WP_More",d:"Strikethrough",h:"WP_Help",p:"WP_Page",x:"WP_Code"},function(e,t){p.shortcuts.add("access+"+t,"",e)}),p.addShortcut("meta+s","",function(){o&&o.autosave&&o.autosave.server.triggerSave()}),1<window.getUserSetting("editor_plain_text_paste_warning")&&(p.settings.paste_plaintext_inform=!1),E.Env.mac&&E.$(p.iframeElement).attr("title",u("Rich Text Area. Press Control-Option-H for help."))}),p.on("PastePlainTextToggle",function(e){!0!==e.state||(e=parseInt(window.getUserSetting("editor_plain_text_paste_warning"),10)||0)<2&&window.setUserSetting("editor_plain_text_paste_warning",++e)}),p.on("preinit",function(){var n,b,t,v,_,y,r=E.ui.Factory,s=p.settings,e=p.getContainer(),x=document.getElementById("wpadminbar"),C=document.getElementById(p.id+"_ifr");function o(e){n&&(n.tempHide||"hide"===e.type||"blur"===e.type?(n.hide(),n=!1):"resizewindow"!==e.type&&"scrollwindow"!==e.type&&"resize"!==e.type&&"scroll"!==e.type||n.blockHide||(clearTimeout(t),t=setTimeout(function(){n&&"function"==typeof n.show&&(n.scrolling=!1,n.show())},250),n.scrolling=!0,n.hide()))}e&&(v=E.$(".mce-toolbar-grp",e)[0],_=E.$(".mce-statusbar",e)[0]),"content"===p.id&&(y=document.getElementById("post-status-info")),p.shortcuts.add("alt+119","",function(){var e;n&&(e=n.find("toolbar")[0])&&e.focus(!0)}),p.on("nodechange",function(e){var t=p.selection.isCollapsed(),t={element:e.element,parents:e.parents,collapsed:t};p.fire("wptoolbar",t),b=t.selection||t.element,n&&n!==t.toolbar&&n.hide(),t.toolbar?n!==t.toolbar?(n=t.toolbar).show():n.reposition():n=!1}),p.on("focus",function(){n&&n.show()}),p.on("resizewindow scrollwindow",o),p.dom.bind(p.getWin(),"resize scroll",o),p.on("remove",function(){p.off("resizewindow scrollwindow",o),p.dom.unbind(p.getWin(),"resize scroll",o)}),p.on("blur hide",o),p.wp=p.wp||{},p.wp._createToolbar=function(e,t){var n,o,a=[];return m(e,function(i){var t;function e(){var e=p.selection;"bullist"===t&&e.selectorChanged("ul > li",function(e,t){for(var n,o=t.parents.length;o--&&"OL"!==(n=t.parents[o].nodeName)&&"UL"!=n;);i.active(e&&"UL"===n)}),"numlist"===t&&e.selectorChanged("ol > li",function(e,t){for(var n,o=t.parents.length;o--&&"OL"!==(n=t.parents[o].nodeName)&&"UL"!==n;);i.active(e&&"OL"===n)}),i.settings.stateSelector&&e.selectorChanged(i.settings.stateSelector,function(e){i.active(e)},!0),i.settings.disabledStateSelector&&e.selectorChanged(i.settings.disabledStateSelector,function(e){i.disabled(e)})}"|"===i?o=null:r.has(i)?(i={type:i},s.toolbar_items_size&&(i.size=s.toolbar_items_size),a.push(i),o=null):(o||(o={type:"buttongroup",items:[]},a.push(o)),p.buttons[i]&&(t=i,(i="function"==typeof(i=p.buttons[t])?i():i).type=i.type||"button",s.toolbar_items_size&&(i.size=s.toolbar_items_size),i=r.create(i),o.items.push(i),p.initialized?e():p.on("init",e)))}),(n=r.create({type:"panel",layout:"stack",classes:"toolbar-grp inline-toolbar-grp",ariaRoot:!0,ariaRemember:!0,items:[{type:"toolbar",layout:"flow",items:a}]})).bottom=t,n.on("show",function(){this.reposition()}),n.on("keydown",function(e){27===e.keyCode&&(this.hide(),p.focus())}),p.on("remove",function(){n.remove()}),n.reposition=function(){if(!b)return this;var e,t=window.pageXOffset||document.documentElement.scrollLeft,n=window.pageYOffset||document.documentElement.scrollTop,o=window.innerWidth,i=window.innerHeight,a=C?C.getBoundingClientRect():{top:0,right:o,bottom:i,left:0,width:o,height:i},r=this.getEl(),s=r.offsetWidth,d=r.clientHeight,l=b.getBoundingClientRect(),c=(l.left+l.right)/2,p=d+5,m=x?x.getBoundingClientRect().bottom:0,u=v?v.getBoundingClientRect().bottom:0,g=_?i-_.getBoundingClientRect().top:0,h=y?i-y.getBoundingClientRect().top:0,f=Math.max(0,m,u,a.top),w=Math.max(0,g,h,i-a.bottom),m=l.top+a.top-f,u=i-a.top-l.bottom-w,g=i-f-w,h="",i=0,w=0;return g<=m||g<=u?(this.scrolling=!0,this.hide(),this.scrolling=!1):(E.Env.iOS&&"IMG"===b.nodeName&&(i=54,w=46),this.bottom?p<=u?(h=" mce-arrow-up",e=l.bottom+a.top+n-w):p<=m&&(h=" mce-arrow-down",e=l.top+a.top+n-d+i):p<=m?(h=" mce-arrow-down",e=l.top+a.top+n-d+i):p<=u&&g/2>l.bottom+a.top-f&&(h=" mce-arrow-up",e=l.bottom+a.top+n-w),void 0===e&&(e=n+f+5+w),c=c-s/2+a.left+t,l.left<0||l.right>a.width?c=a.left+t+(a.width-s)/2:o<=s?(h+=" mce-arrow-full",c=0):c<0&&l.left+s>o||o<c+s&&l.right-s<0?c=(o-s)/2:c<a.left+t?(h+=" mce-arrow-left",c=l.left+a.left+t):c+s>a.width+a.left+t&&(h+=" mce-arrow-right",c=l.right-s+a.left+t),E.Env.iOS&&"IMG"===b.nodeName&&(h=h.replace(/ ?mce-arrow-(up|down)/g,"")),r.className=r.className.replace(/ ?mce-arrow-[\w]+/g,"")+h,P.setStyles(r,{left:c,top:e})),this},n.hide().renderTo(document.body),n}},!0),{_showButtons:n,_hideButtons:n,_setEmbed:n,_getEmbed:n}})}(window.tinymce);