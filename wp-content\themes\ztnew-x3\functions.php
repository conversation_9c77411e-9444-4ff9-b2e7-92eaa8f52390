<?php
include (TEMPLATEPATH . '/includes/metabox.php');
//cat images
include( 'includes/categories-images.php' );
//theme options
include( 'admin-option/theme-option.php' );
//cat template
include( 'includes/cat-template.php' );
//seo
$DX_seo = get_option( 'other-2' );
if( $DX_seo[0] == 'on' ) include( 'includes/seo/seo.php' );
//theme info
include( 'includes/info.php' );
/**
 * 移除菜单的多余CSS选择器
 */
add_filter('nav_menu_css_class', 'my_css_attributes_filter', 100, 1);
add_filter('nav_menu_item_id', 'my_css_attributes_filter', 100, 1);
add_filter('page_css_class', 'my_css_attributes_filter', 100, 1);
function my_css_attributes_filter($var) {
	return is_array($var) ? array_intersect($var, array('current-menu-item','current-post-ancestor','current-menu-ancestor','current-menu-parent','menu-item-has-children')) : '';
}
//侧边栏分类
function get_category_root_id($cat)  
{  
$this_category = get_category($cat); // 取得当前分类  
while($this_category->category_parent) // 若当前分类有上级分类时，循环  
{  
$this_category = get_category($this_category->category_parent); // 将当前分类设为上级分类（往上爬）  
}  
return $this_category->term_id; // 返回根分类的id号  
}
//谷歌字体
function remove_open_sans() {
    wp_deregister_style( 'open-sans' );
    wp_register_style( 'open-sans', false );
    wp_enqueue_style('open-sans','');
}
add_action( 'init', 'remove_open_sans' );

//移除顶部多余信息
remove_action( 'wp_head', 'wp_enqueue_scripts', 1 ); //Javascript的调用
remove_action( 'wp_head', 'feed_links', 2 ); //移除feed
remove_action( 'wp_head', 'feed_links_extra', 3 ); //移除feed
remove_action( 'wp_head', 'rsd_link' ); //移除离线编辑器开放接口
remove_action( 'wp_head', 'wlwmanifest_link' );  //移除离线编辑器开放接口
remove_action( 'wp_head', 'index_rel_link' );//去除本页唯一链接信息
remove_action('wp_head', 'parent_post_rel_link', 10, 0 );//清除前后文信息
remove_action('wp_head', 'start_post_rel_link', 10, 0 );//清除前后文信息
remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0 );
remove_action( 'wp_head', 'locale_stylesheet' );
remove_action('publish_future_post','check_and_publish_future_post',10, 1 );
remove_action( 'wp_head', 'noindex', 1 );
remove_action( 'wp_head', 'wp_print_styles', 8 );//载入css
remove_action( 'wp_head', 'wp_print_head_scripts', 9 );
remove_action( 'wp_head', 'wp_generator' ); //移除WordPress版本
remove_action( 'wp_head', 'rel_canonical' );
remove_action( 'wp_footer', 'wp_print_footer_scripts' );
remove_action( 'wp_head', 'wp_shortlink_wp_head', 10, 0 );
remove_action( 'template_redirect', 'wp_shortlink_header', 11, 0 );
add_action('widgets_init', 'my_remove_recent_comments_style');
function my_remove_recent_comments_style() {
global $wp_widget_factory;
remove_action('wp_head', array($wp_widget_factory->widgets['WP_Widget_Recent_Comments'] ,'recent_comments_style'));
}
//禁用REST API、移除wp-json链接
add_filter('rest_enabled', '_return_false');
add_filter('rest_jsonp_enabled', '_return_false');
remove_action( 'wp_head', 'rest_output_link_wp_head', 10 );
remove_action( 'wp_head', 'wp_oembed_add_discovery_links', 10 );
//禁用embeds功能
function disable_emojis() {  
remove_action( 'wp_head', 'print_emoji_detection_script', 7 );  
remove_action( 'admin_print_scripts', 'print_emoji_detection_script' );  
remove_action( 'wp_print_styles', 'print_emoji_styles' );  
remove_action( 'admin_print_styles', 'print_emoji_styles' );  
remove_filter( 'the_content_feed', 'wp_staticize_emoji' );  
remove_filter( 'comment_text_rss', 'wp_staticize_emoji' );  
remove_filter( 'wp_mail', 'wp_staticize_emoji_for_email' );  
add_filter( 'tiny_mce_plugins', 'disable_emojis_tinymce' );  
}  
add_action( 'init', 'disable_emojis' );  
/** 
* Filter function used to remove the tinymce emoji plugin. 
*/  
function disable_emojis_tinymce( $plugins ) {  
if ( is_array( $plugins ) ) {  
return array_diff( $plugins, array( 'wpemoji' ) );  
} else {  
return array();  
}  
}  
   
function disable_embeds_init() {  
global $wp;  
$wp->public_query_vars = array_diff( $wp->public_query_vars, array( 'embed', ) );  
remove_action( 'rest_api_init', 'wp_oembed_register_route' );  
add_filter( 'embed_oembed_discover', '__return_false' );  
remove_filter( 'oembed_dataparse', 'wp_filter_oembed_result', 10 );  
remove_action( 'wp_head', 'wp_oembed_add_discovery_links' );  
remove_action( 'wp_head', 'wp_oembed_add_host_js' );  
add_filter( 'tiny_mce_plugins', 'disable_embeds_tiny_mce_plugin' );  
add_filter( 'rewrite_rules_array', 'disable_embeds_rewrites' ); }  
add_action( 'init', 'disable_embeds_init', 9999 );  
function disable_embeds_tiny_mce_plugin( $plugins ) { return array_diff( $plugins, array( 'wpembed' ) ); }  
function disable_embeds_rewrites( $rules ) { foreach ( $rules as $rule => $rewrite ) { if ( false !== strpos( $rewrite, 'embed=true' ) ) { unset( $rules[ $rule ] ); } }  
return $rules; }  
function disable_embeds_remove_rewrite_rules() { add_filter( 'rewrite_rules_array', 'disable_embeds_rewrites' ); flush_rewrite_rules(); }  
register_activation_hook( __FILE__, 'disable_embeds_remove_rewrite_rules' );  
function disable_embeds_flush_rewrite_rules() { remove_filter( 'rewrite_rules_array', 'disable_embeds_rewrites' ); flush_rewrite_rules(); }  
register_deactivation_hook( __FILE__, 'disable_embeds_flush_rewrite_rules' );  
//禁止加载WP自带的jquery.js
if ( !is_admin() ) { // 后台不禁止
function my_init_method() {
wp_deregister_script( 'jquery' ); // 取消原有的 jquery 定义
}
add_action('init', 'my_init_method'); 
}
wp_deregister_script( 'l10n' );

//注册导航
register_nav_menus(
      array(
       'main' => __( '主菜单导航' )
      )
   );
   
//禁止代码标点转换
remove_filter('the_content', 'wptexturize');

//编辑器增强
 function enable_more_buttons($buttons) {
     $buttons[] = 'hr';
     $buttons[] = 'del';
     $buttons[] = 'sub';
     $buttons[] = 'sup'; 
     $buttons[] = 'fontselect';
     $buttons[] = 'fontsizeselect';
     $buttons[] = 'cleanup';   
     $buttons[] = 'styleselect';
     $buttons[] = 'wp_page';
     $buttons[] = 'anchor';
     $buttons[] = 'backcolor';
     return $buttons;
     }
add_filter("mce_buttons_3", "enable_more_buttons");

//给文章图片自动添加alt和title信息
add_filter('the_content', 'imagesalt');
function imagesalt($content) {
       global $post;
       $pattern ="/<a(.*?)href=('|\")(.*?).(bmp|gif|jpeg|jpg|png)('|\")(.*?)>/i";
       $replacement = '<a$1href=$2$3.$4$5 alt="'.$post->post_title.'" title="'.$post->post_title.'"$6>';
       $content = preg_replace($pattern, $replacement, $content);
       return $content;
}

/*激活友情链接后台*/
add_filter( 'pre_option_link_manager_enabled', '__return_true' );

//添加特色缩略图支持
if ( function_exists('add_theme_support') )add_theme_support('post-thumbnails');

//输出缩略图地址
function post_thumbnail_src(){
	global $post;
	if( $values = get_post_custom_values("thumb") ) {	//输出自定义域图片地址
		$values = get_post_custom_values("thumb");
		$post_thumbnail_src = $values [0];
	} elseif( has_post_thumbnail() ){    //如果有特色缩略图，则输出缩略图地址
		$thumbnail_src = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID),'full');
		$post_thumbnail_src = $thumbnail_src [0];
	} else {
		$post_thumbnail_src = '';
		ob_start();
		ob_end_clean();
		$output = preg_match_all('/<img.+src=[\'"]([^\'"]+)[\'"].*>/i', $post->post_content, $matches);
		if(!empty($matches[1][0])){
			$post_thumbnail_src = $matches[1][0];   //获取该图片 src
		}else{	//如果日志中没有图片，则显示随机图片
			$random = mt_rand(1, 5);
			$post_thumbnail_src = get_template_directory_uri().'/images/random/'.$random.'.jpg';
			//如果日志中没有图片，则显示默认图片
			//$post_thumbnail_src = get_template_directory_uri().'/images/default_thumb.jpg';
		}
	};
	echo $post_thumbnail_src;
}

//面包屑
function get_breadcrumbs()  
{  
    global $wp_query;  
    if ( !is_home() ){  
        // Start the UL  
        echo '';  
        // Add the Home link  
        echo '<a href="'. get_settings('home') .'">首页</a>';  
    
        if ( is_category() )  
        {  
            $catTitle = single_cat_title( "", false );  
            $cat = get_cat_ID( $catTitle );  
            echo " &raquo; ". get_category_parents( $cat, TRUE, " &raquo; " ) ."";  
        }  
        elseif ( is_archive() && !is_category() )  
        {  
            echo " &raquo; Archives";  
        }  
        elseif ( is_search() ) {  
    
            echo " &raquo; Search Results";  
        }  
        elseif ( is_404() )  
        {  
            echo " &raquo; 404 Not Found";  
        }  
        elseif ( is_single() )  
        {  
            $category = get_the_category();  
            $category_id = get_cat_ID( $category[0]->cat_name );  
    
            echo ' &raquo; '. get_category_parents( $category_id, TRUE, "  " );  
              
        }  
        elseif ( is_page() )  
        {  
            $post = $wp_query->get_queried_object();  
    
            if ( $post->post_parent == 0 ){  
    
                echo " &raquo; ".the_title('','', FALSE)."";  
    
            } else {  
                $title = the_title('','', FALSE);  
                $ancestors = array_reverse( get_post_ancestors( $post->ID ) );  
                array_push($ancestors, $post->ID);  
    
                foreach ( $ancestors as $ancestor ){  
                    if( $ancestor != end($ancestors) ){  
                        echo ' &raquo; <a href="'. get_permalink($ancestor) .'">'. strip_tags( apply_filters( 'single_post_title', get_the_title( $ancestor ) ) ) .'</a>';  
                    } else {  
                        echo ' &raquo; '. strip_tags( apply_filters( 'single_post_title', get_the_title( $ancestor ) ) ) .'';  
                    }  
                }  
            }  
        }  
        // End the UL  
        echo "";  
    }  
}  


//分类ID加入后台面板
function exampletheme_options_after() { 
	global $wpdb;
	$request = "SELECT $wpdb->terms.term_id, name FROM $wpdb->terms ";
	$request .= " LEFT JOIN $wpdb->term_taxonomy ON $wpdb->term_taxonomy.term_id = $wpdb->terms.term_id ";
	$request .= " WHERE $wpdb->term_taxonomy.taxonomy = 'category' ";
	$request .= " ORDER BY term_id asc";
	$categorys = $wpdb->get_results($request);
	echo '<div class="uk-panel uk-panel-box" style="margin-bottom: 20px;"><h3 style="margin-top: 0; margin-bottom: 15px; font-size: 14px; line-height: 24px; font-weight: 400; text-transform: none; color: #fff;background: gray;padding-left: 8px;">可能会用到的分类ID</h3>';
	echo "<ul>";
	foreach ($categorys as $category) { 
		echo  '<li style="margin-right: 10px;float:left;">'.$category->name."（<code>".$category->term_id.'</code>）</li>';
	}
	echo "</ul></div>";
}

//分页
function par_pagenavi($range = 9){ 
if ( is_singular() ) return;
global $wp_query, $paged;
$max_page = $wp_query->max_num_pages;
if ( $max_page == 1 ) return;
if ( empty( $paged ) ) $paged = 1;
echo ' ';
    global $paged, $wp_query;  
    if ( !$max_page ) {$max_page = $wp_query->max_num_pages;}  
    if($max_page > 1){if(!$paged){$paged = 1;}  
    if($paged != 1){echo "<a href='" . get_pagenum_link(1) . "' class='extend' title='跳转到首页'> 首页 </a>";}  
    previous_posts_link(' 上一页 ');  
    if($max_page > $range){  
        if($paged < $range){for($i = 1; $i <= ($range + 1); $i++){echo "<a href='" . get_pagenum_link($i) ."'";  
        if($i==$paged)echo " class='current'";echo ">$i</a>";}}  
    elseif($paged >= ($max_page - ceil(($range/2)))){  
        for($i = $max_page - $range; $i <= $max_page; $i++){echo "<a href='" . get_pagenum_link($i) ."'";  
        if($i==$paged)echo " class='current'";echo ">$i</a>";}}  
    elseif($paged >= $range && $paged < ($max_page - ceil(($range/2)))){  
        for($i = ($paged - ceil($range/2)); $i <= ($paged + ceil(($range/2))); $i++){echo "<a href='" . get_pagenum_link($i) ."'";if($i==$paged) echo " class='current'";echo ">$i</a>";}}}  
    else{for($i = 1; $i <= $max_page; $i++){echo "<a href='" . get_pagenum_link($i) ."'";  
    if($i==$paged)echo " class='current'";echo ">$i</a>";}}  
    next_posts_link(' 下一页 ');  
    if($paged != $max_page){echo "<a href='" . get_pagenum_link($max_page) . "' class='extend' title='跳转到最后一页'> 末页 </a>";}}  
}  