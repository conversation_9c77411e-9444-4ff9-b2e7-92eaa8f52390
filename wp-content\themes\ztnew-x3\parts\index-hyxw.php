<div class="col-3-2 last">
	<div class="module-section">
		<div class="module-section-inner">
			<div class="page-width">
				<div class="module-section-title-wrapper clearfix">
					<div class="module-section-title clearfix" style="">
						<h2><?php echo get_cat_name( $cid ); ?></h2>
					</div>
					<div class="module-section-more">
						<a href="<?php echo get_category_link( $cid ); ?>" target="_blank">查看更多</a>
					</div>
				</div>
				<div class="headlines-list-2col clearfix">
				<?php $first=1; ?>   
				<?php if (have_posts()) : ?>   
					<?php 
					query_posts( array( 'cat'=>$cid, 'posts_per_page'=>4, 'ignore_sticky_posts'=>true ) );
					while( have_posts() ): the_post(); 
				?>    
				<?php if ($first) : $first = $first -1  ?>   
					<div class="headlines-content not-animated" data-animate="fadeInUp" data-delay="200">
						<p>
							<a href="<?php the_permalink(); ?>" target="_blank"><img src="<?php echo get_template_directory_uri(); ?>/timthumb.php?src=<?php echo post_thumbnail_src(); ?>&w=353&h=143&zc=1" alt="<?php the_title(); ?>"/></a>
						</p>
						<h2><a href="<?php the_permalink(); ?>" target="_blank"><?php the_title_attribute(); ?></a></h2>
						<div class="headlines-content-summary">
							<div class="qhd-content">
								<p>
									<?php echo mb_strimwidth(strip_tags($post->post_content),0,310,'...');?>
								</p>
							</div>
						</div>
					</div>
					  <?php else : ?> 
					<div class="headlines-others not-animated" data-animate="fadeInUp" data-delay="400">
						<div class="entry-list entry-list-time-hl entry-set-time-hl">
 
							<div class="entry-item">
								<div class="time">
									<p class="time-day"><?php the_time('d') ?></p>
									<p class="time-date"><?php the_time('Y-m') ?></p>
								</div>
								<div class="entry-title">
									<h2><a href="<?php the_permalink(); ?>" target="_blank"><?php the_title_attribute(); ?></a></h2>
								</div>
								<div class="entry-summary">
									<div class="qhd-content">
										<p>
											<?php echo mb_strimwidth(strip_tags($post->post_content),0,120,'...');?>
										</p>
									</div>
								</div>
							</div>
				<?php endif; ?>   
				<?php endwhile; ?>   
				<?php endif; ?>  
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>