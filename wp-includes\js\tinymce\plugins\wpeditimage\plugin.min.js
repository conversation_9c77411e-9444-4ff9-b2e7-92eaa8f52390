tinymce.PluginManager.add("wpeditimage",function(g){var r,t,n,c,a,e=tinymce.each,l=tinymce.trim,i=tinymce.Env.iOS;function o(e){return!(!g.dom.getAttrib(e,"data-mce-placeholder")&&!g.dom.getAttrib(e,"data-mce-object"))}function d(e){e=g.$(e).parents("[contenteditable]");return e&&"false"===e.attr("contenteditable")}function s(e){return e.replace(/(?:<p>)?\[(?:wp_)?caption([^\]]+)\]([\s\S]+?)\[\/(?:wp_)?caption\](?:<\/p>)?/g,function(e,t,n){var a,i,o,r,c,d=t.match(/id=['"]([^'"]*)['"] ?/);return(c=(t=(i=(t=(a=(t=d?t.replace(d[0],""):t).match(/align=['"]([^'"]*)['"] ?/))?t.replace(a[0],""):t).match(/class=['"]([^'"]*)['"] ?/))?t.replace(i[0],""):t).match(/width=['"]([0-9]*)['"] ?/))&&(t=t.replace(c[0],"")),r=(r=(n=l(n)).match(/((?:<a [^>]+>)?<img [^>]+>(?:<\/a>)?)([\s\S]*)/i))&&r[2]?(o=l(r[2]),l(r[1])):(o=l(t).replace(/caption=['"]/,"").replace(/['"]$/,""),n),d=d&&d[1]?d[1].replace(/[<>&]+/g,""):"",a=a&&a[1]?a[1]:"alignnone",i=i&&i[1]?" "+i[1].replace(/[<>&]+/g,""):"",(c=(c=!c&&r?r.match(/width=['"]([0-9]*)['"]/):c)&&c[1]?c[1]:c)&&o?(c=parseInt(c,10),g.getParam("wpeditimage_html5_captions")||(c+=10),'<div class="mceTemp"><dl id="'+d+'" class="wp-caption '+a+i+'" style="width: '+c+'px"><dt class="wp-caption-dt">'+r+'</dt><dd class="wp-caption-dd">'+o+"</dd></dl></div>"):n})}function p(e){return e.replace(/(?:<div [^>]+mceTemp[^>]+>)?\s*(<dl [^>]+wp-caption[^>]+>[\s\S]+?<\/dl>)\s*(?:<\/div>)?/g,function(e,t){var n="";return-1===t.indexOf("<img ")||-1!==t.indexOf("</p>")?t.replace(/<d[ldt]( [^>]+)?>/g,"").replace(/<\/d[ldt]>/g,""):-1===(n=t.replace(/\s*<dl ([^>]+)>\s*<dt [^>]+>([\s\S]+?)<\/dt>\s*<dd [^>]+>([\s\S]*?)<\/dd>\s*<\/dl>\s*/gi,function(e,t,n,a){var i,o,r=n.match(/width="([0-9]*)"/);return r=r&&r[1]?r[1]:"",o=(i=(i=t.match(/class="([^"]*)"/))&&i[1]?i[1]:"").match(/align[a-z]+/i)||"alignnone",r&&a?'[caption id="'+(t=(t=t.match(/id="([^"]*)"/))&&t[1]?t[1]:"")+'" align="'+o+'" width="'+r+'"'+(i=(i=i.replace(/wp-caption ?|align[a-z]+ ?/gi,""))&&' class="'+i+'"')+"]"+n+" "+(a=(a=a.replace(/\r\n|\r/g,"\n").replace(/<[a-zA-Z0-9]+( [^<>]+)?>/g,function(e){return e.replace(/[\r\n\t]+/," ")})).replace(/\s*\n\s*/g,"<br />"))+"[/caption]":"alignnone"!==o[0]?n.replace(/><img/,' class="'+o[0]+'"><img'):n})).indexOf("[caption")?t.replace(/[\s\S]*?((?:<a [^>]+>)?<img [^>]+>(?:<\/a>)?)(<p>[\s\S]*<\/p>)?[\s\S]*/gi,"<p>$1</p>$2"):n})}function m(e){return e&&(e.textContent||e.innerText)}function u(e){return!e||-1===e.indexOf("<")&&-1===e.indexOf(">")?e:(t=t||new tinymce.html.Serializer({},g.schema)).serialize(g.parser.parse(e,{forced_root_block:!1}))}function h(e){var t=g.dom.getParent(e,"div.mceTemp");(t=!t&&"IMG"===e.nodeName?g.dom.getParent(e,"a"):t)?(t.nextSibling?g.selection.select(t.nextSibling):t.previousSibling?g.selection.select(t.previousSibling):g.selection.select(t.parentNode),g.selection.collapse(!0),g.dom.remove(t)):g.dom.remove(e),g.nodeChanged(),g.undoManager.add()}return g.addButton("wp_img_remove",{tooltip:"Remove",icon:"dashicon dashicons-no",onclick:function(){h(g.selection.getNode())}}),g.addButton("wp_img_edit",{tooltip:"Edit ",icon:"dashicon dashicons-edit",onclick:function(){var p,e,t;p=g.selection.getNode(),"undefined"!=typeof wp&&wp.media?(t=function(e){var t,n,a,i,o=[],r=g.dom,c=/^\d+$/;(n={attachment_id:!1,size:"custom",caption:"",align:"none",extraClasses:"",link:!1,linkUrl:"",linkClassName:"",linkTargetBlank:!1,linkRel:"",title:""}).url=r.getAttrib(e,"src"),n.alt=r.getAttrib(e,"alt"),n.title=r.getAttrib(e,"title"),a=r.getAttrib(e,"width"),i=r.getAttrib(e,"height"),(!c.test(a)||parseInt(a,10)<1)&&(a=e.naturalWidth||e.width);(!c.test(i)||parseInt(i,10)<1)&&(i=e.naturalHeight||e.height);n.customWidth=n.width=a,n.customHeight=n.height=i,a=tinymce.explode(e.className," "),t=[],tinymce.each(a,function(e){/^wp-image/.test(e)?n.attachment_id=parseInt(e.replace("wp-image-",""),10):/^align/.test(e)?n.align=e.replace("align",""):/^size/.test(e)?n.size=e.replace("size-",""):t.push(e)}),n.extraClasses=t.join(" "),(i=r.getParents(e,".wp-caption")).length&&(i=i[0],a=i.className.split(" "),tinymce.each(a,function(e){/^align/.test(e)?n.align=e.replace("align",""):e&&"wp-caption"!==e&&o.push(e)}),n.captionClassName=o.join(" "),(i=r.select("dd.wp-caption-dd",i)).length&&(i=i[0],n.caption=g.serializer.serialize(i).replace(/<br[^>]*>/g,"$&\n").replace(/^<p>/,"").replace(/<\/p>$/,"")));e.parentNode&&"A"===e.parentNode.nodeName&&(e=e.parentNode,n.linkUrl=r.getAttrib(e,"href"),n.linkTargetBlank="_blank"===r.getAttrib(e,"target"),n.linkRel=r.getAttrib(e,"rel"),n.linkClassName=e.className);return n}(p),wp.media.events.trigger("editor:image-edit",{editor:g,metadata:t,image:p}),e=wp.media({frame:"image",state:"image-details",metadata:t}),wp.media.events.trigger("editor:frame-create",{frame:e}),t=function(s){g.focus(),g.undoManager.transact(function(){var e,t,n,a,i,o,r,c,d,l;e=p,t=s,l=g.dom,(c=tinymce.explode(t.extraClasses," "))||(c=[]),t.caption||c.push("align"+t.align),t.attachment_id&&(c.push("wp-image-"+t.attachment_id),t.size&&"custom"!==t.size&&c.push("size-"+t.size)),o=t.width,d=t.height,"custom"===t.size&&(o=t.customWidth,d=t.customHeight),i={src:t.url,width:o||null,height:d||null,title:t.title||null,"class":c.join(" ")||null},l.setAttribs(e,i),g.$(e).attr("alt",t.alt||""),r={href:t.linkUrl,rel:t.linkRel||null,target:t.linkTargetBlank?"_blank":null,"class":t.linkClassName||null},e.parentNode&&"A"===e.parentNode.nodeName&&!m(e.parentNode)?t.linkUrl?l.setAttribs(e.parentNode,r):l.remove(e.parentNode,!0):t.linkUrl&&((a=l.getParent(e,"a"))&&l.insertAfter(e,a),a=l.create("a",r),e.parentNode.insertBefore(a,e),a.appendChild(e)),d=g.dom.getParent(e,".mceTemp"),c=e.parentNode&&"A"===e.parentNode.nodeName&&!m(e.parentNode)?e.parentNode:e,t.caption?(t.caption=u(t.caption),i=t.attachment_id?"attachment_"+t.attachment_id:null,r="align"+(t.align||"none"),a="wp-caption "+r,t.captionClassName&&(a+=" "+t.captionClassName.replace(/[<>&]+/g,"")),g.getParam("wpeditimage_html5_captions")||(o=parseInt(o,10),o+=10),d?((r=l.select("dl.wp-caption",d)).length&&l.setAttribs(r,{id:i,"class":a,style:"width: "+o+"px"}),(r=l.select(".wp-caption-dd",d)).length&&l.setHTML(r[0],t.caption)):(n="<dl "+(i=i?'id="'+i+'" ':"")+'class="'+a+'" style="width: '+o+'px"><dt class="wp-caption-dt"></dt><dd class="wp-caption-dd">'+t.caption+"</dd></dl>",o=l.create("div",{"class":"mceTemp"},n),(n=l.getParent(c,"p"))?n.parentNode.insertBefore(o,n):c.parentNode.insertBefore(o,c),g.$(o).find("dt.wp-caption-dt").append(c),n&&l.isEmpty(n)&&l.remove(n))):d&&(n=l.create("p"),d.parentNode.insertBefore(n,d),n.appendChild(c),l.remove(d)),c=g.$(e),l=c.attr("srcset"),d=c.attr("src"),l&&d&&(d=d.replace(/[?#].*/,""),-1===l.indexOf(d)&&c.attr("srcset",null).attr("sizes",null)),wp.media.events&&wp.media.events.trigger("editor:image-update",{editor:g,metadata:t,image:e}),g.nodeChanged()}),e.detach()},e.state("image-details").on("update",t),e.state("replace-image").on("replace",t),e.on("close",function(){g.focus(),e.detach()}),e.open()):g.execCommand("mceImage")}}),e({alignleft:"Align left",aligncenter:"Align center",alignright:"Align right",alignnone:"No alignment"},function(e,n){var t=n.slice(5);g.addButton("wp_img_"+n,{tooltip:e,icon:"dashicon dashicons-align-"+t,cmd:"alignnone"===n?"wpAlignNone":"Justify"+t.slice(0,1).toUpperCase()+t.slice(1),onPostRender:function(){var t=this;g.on("NodeChange",function(e){"IMG"===e.element.nodeName&&(e=g.dom.getParent(e.element,".wp-caption")||e.element,"alignnone"===n?t.active(!/\balign(left|center|right)\b/.test(e.className)):t.active(g.dom.hasClass(e,n)))})}})}),g.once("preinit",function(){g.wp&&g.wp._createToolbar&&(r=g.wp._createToolbar(["wp_img_alignleft","wp_img_aligncenter","wp_img_alignright","wp_img_alignnone","wp_img_edit","wp_img_remove"]))}),g.on("wptoolbar",function(e){"IMG"!==e.element.nodeName||o(e.element)||(e.toolbar=r)}),i&&g.on("init",function(){g.on("touchstart",function(e){"IMG"!==e.target.nodeName||d(e.target)||(n=!0)}),g.dom.bind(g.getDoc(),"touchmove",function(){n=!1}),g.on("touchend",function(e){var t;n&&"IMG"===e.target.nodeName&&!d(e.target)?(t=e.target,n=!1,window.setTimeout(function(){g.selection.select(t),g.nodeChanged()},100)):r&&r.hide()})}),g.on("init",function(){var m=g.dom,e=g.getParam("wpeditimage_html5_captions")?"html5-captions":"html4-captions";m.addClass(g.getBody(),e),g.on("wpLoadImageForm",function(e){g.getParam("wpeditimage_disable_captions")||e.data.splice(e.data.length-1,0,{type:"textbox",flex:1,name:"wpcaption",minHeight:60,multiline:!0,scroll:!0,label:"Image caption"})}),g.on("wpNewImageRefresh",function(e){var t;(t=m.getParent(e.node,"dl.wp-caption"))&&(t.style.width||(e=(e=parseInt(e.node.clientWidth,10)+10)?e+"px":"50%",m.setStyle(t,"width",e)))}),g.on("wpImageFormSubmit",function(e){var t,n,a,i,o=e.imgData.data,r=e.imgData.node,c=e.imgData.wpcaption,d="",l="",s="",p=null;o.id="__wp-temp-img-id",e.imgData.cancel=!0,o.style||(o.style=null),o.src?(c=c&&u(c=(c=c.replace(/\r\n|\r/g,"\n").replace(/<\/?[a-zA-Z0-9]+( [^<>]+)?>/g,function(e){return e.replace(/[\r\n\t]+/," ")})).replace(/(<br[^>]*>)\s*\n\s*/g,"$1").replace(/\s*\n\s*/g,"<br />")),r?(p=r.id||null,m.setAttribs(r,o),t=m.getParent(r,"dl.wp-caption"),c?t?(n=m.select("dd.wp-caption-dd",t)[0])&&(n.innerHTML=c):(r.className&&(d=r.className.match(/wp-image-([0-9]+)/),l=r.className.match(/align(left|right|center|none)/)),l?(l=l[0],r.className=r.className.replace(/align(left|right|center|none)/g,"")):l="alignnone",l=' class="wp-caption '+l+'"',d=d&&' id="attachment_'+d[1]+'"',(s=o.width||r.clientWidth)&&(s=parseInt(s,10),g.getParam("wpeditimage_html5_captions")||(s+=10),s=' style="width: '+s+'px"'),a=r.parentNode&&"A"===r.parentNode.nodeName?r.parentNode:r,i="<dl "+d+l+s+'><dt class="wp-caption-dt"></dt><dd class="wp-caption-dd">'+c+"</dd></dl>",t=m.create("div",{"class":"mceTemp"},i),(n=m.getParent(a,"p"))?n.parentNode.insertBefore(t,n):a.parentNode.insertBefore(t,a),g.$(t).find("dt.wp-caption-dt").append(a),n&&m.isEmpty(n)&&m.remove(n)):t&&(i="A"===r.parentNode.nodeName?m.getOuterHTML(r.parentNode):m.getOuterHTML(r),n=m.create("p",{},i),m.insertAfter(n,t.parentNode),g.selection.select(n),g.nodeChanged(),m.remove(t.parentNode))):(i=m.createHTML("img",o),c?(a=g.selection.getNode(),o.width&&(s=parseInt(o.width,10),g.getParam("wpeditimage_html5_captions")||(s+=10),s=' style="width: '+s+'px"'),i='<dl class="wp-caption alignnone"'+s+'><dt class="wp-caption-dt">'+i+'</dt><dd class="wp-caption-dd">'+c+"</dd></dl>",(n="P"===a.nodeName?a:m.getParent(a,"p"))&&"P"===n.nodeName?(t=m.create("div",{"class":"mceTemp"},i),n.parentNode.insertBefore(t,n),g.selection.select(t),g.nodeChanged(),m.isEmpty(n)&&m.remove(n)):g.selection.setContent('<div class="mceTemp">'+i+"</div>")):g.selection.setContent(i)),r=m.get("__wp-temp-img-id"),m.setAttrib(r,"id",p||null),e.imgData.node=r):r&&((t=m.getParent(r,"div.mceTemp"))?m.remove(t):"A"===r.parentNode.nodeName?m.remove(r.parentNode):m.remove(r),g.nodeChanged())}),g.on("wpLoadImageData",function(e){var t=e.imgData.data,e=e.imgData.node;(e=m.getParent(e,"dl.wp-caption"))&&(e=m.select("dd.wp-caption-dd",e)[0])&&(t.wpcaption=g.serializer.serialize(e).replace(/<br[^>]*>/g,"$&\n").replace(/^<p>/,"").replace(/<\/p>$/,""))}),tinymce.Env.ie&&10<tinymce.Env.ie&&m.bind(g.getBody(),"mscontrolselect",function(e){"IMG"===e.target.nodeName&&m.getParent(e.target,".wp-caption")?g.getBody().focus():"DL"===e.target.nodeName&&m.hasClass(e.target,"wp-caption")&&e.target.focus()})}),g.on("ObjectResized",function(a){var i=a.target;"IMG"===i.nodeName&&g.undoManager.transact(function(){var e,t,n=g.dom;i.className=i.className.replace(/\bsize-[^ ]+/,""),(e=n.getParent(i,".wp-caption"))&&(t=a.width||n.getAttrib(i,"width"))&&(t=parseInt(t,10),g.getParam("wpeditimage_html5_captions")||(t+=10),n.setStyle(e,"width",t+"px"))})}),g.on("pastePostProcess",function(e){g.dom.getParent(g.selection.getNode(),"dd.wp-caption-dd")&&(g.$("img, audio, video, object, embed, iframe, script, style",e.node).remove(),g.$("*",e.node).each(function(e,t){g.dom.isBlock(t)&&(tinymce.trim(t.textContent||t.innerText)?(g.dom.insertAfter(g.dom.create("br"),t),g.dom.remove(t,!0)):g.dom.remove(t))}),g.$("br",e.node).each(function(e,t){t.nextSibling&&"BR"!==t.nextSibling.nodeName&&t.previousSibling&&"BR"!==t.previousSibling.nodeName||g.dom.remove(t)}),c=!0)}),g.on("BeforeExecCommand",function(e){var t,n,a,i=e.command,o=g.dom;if("mceInsertContent"===i||"Indent"===i||"Outdent"===i){if(t=g.selection.getNode(),a=o.getParent(t,"div.mceTemp")){if("mceInsertContent"!==i)return e.preventDefault(),e.stopImmediatePropagation(),!1;c?c=!1:(n=o.create("p"),o.insertAfter(n,a),g.selection.setCursorLocation(n,0),g.nodeChanged())}}else"JustifyLeft"!==i&&"JustifyRight"!==i&&"JustifyCenter"!==i&&"wpAlignNone"!==i||(t=g.selection.getNode(),a="align"+i.slice(7).toLowerCase(),n=g.dom.getParent(t,".wp-caption"),"IMG"!==t.nodeName&&!n||(t=n||t,a=g.dom.hasClass(t,a)?" alignnone":" "+a,t.className=l(t.className.replace(/ ?align(left|center|right|none)/g,"")+a),g.nodeChanged(),e.preventDefault(),r&&r.reposition(),g.fire("ExecCommand",{command:i,ui:e.ui,value:e.value})))}),g.on("keydown",function(e){var t,n,a,i=g.selection,o=e.keyCode,r=g.dom,c=tinymce.util.VK;if(o===c.ENTER)t=i.getNode(),(n=r.getParent(t,"div.mceTemp"))&&(r.events.cancel(e),tinymce.each(r.select("dt, dd",n),function(e){r.isEmpty(e)&&r.remove(e)}),a=tinymce.Env.ie&&tinymce.Env.ie<11?"":'<br data-mce-bogus="1" />',a=r.create("p",null,a),"DD"===t.nodeName?r.insertAfter(a,n):n.parentNode.insertBefore(a,n),g.nodeChanged(),i.setCursorLocation(a,0));else if((o===c.DELETE||o===c.BACKSPACE)&&("DIV"===(t=i.getNode()).nodeName&&r.hasClass(t,"mceTemp")?n=t:"IMG"!==t.nodeName&&"DT"!==t.nodeName&&"A"!==t.nodeName||(n=r.getParent(t,"div.mceTemp")),n))return r.events.cancel(e),h(t),!1}),tinymce.Env.gecko&&g.on("undo redo",function(){"IMG"===g.selection.getNode().nodeName&&g.selection.collapse()}),g.wpSetImgCaption=s,g.wpGetImgCaption=p,g.on("beforeGetContent",function(e){"raw"!==e.format&&g.$('img[id="__wp-temp-img-id"]').attr("id",null)}),g.on("BeforeSetContent",function(e){"raw"!==e.format&&(e.content=g.wpSetImgCaption(e.content))}),g.on("PostProcess",function(e){e.get&&(e.content=g.wpGetImgCaption(e.content))}),g.on("dragstart",function(){var e=g.selection.getNode();"IMG"===e.nodeName&&((a=g.dom.getParent(e,".mceTemp"))||"A"!==e.parentNode.nodeName||m(e.parentNode)||(a=e.parentNode))}),g.on("drop",function(e){var t=g.dom,n=tinymce.dom.RangeUtils.getCaretRangeFromPoint(e.clientX,e.clientY,g.getDoc());n&&t.getParent(n.startContainer,".mceTemp")?e.preventDefault():a&&(e.preventDefault(),g.undoManager.transact(function(){n&&g.selection.setRng(n),g.selection.setNode(a),t.remove(a)})),a=null}),g.wp=g.wp||{},g.wp.isPlaceholder=o,{_do_shcode:s,_get_shcode:p}});