/*!
 * jQuery UI Effects Bounce 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/bounce-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(g){return g.effects.effect.bounce=function(e,t){var i,o,f,c=g(this),n=["position","top","bottom","left","right","height","width"],a=g.effects.setMode(c,e.mode||"effect"),s="hide"===a,p="show"===a,u=e.direction||"up",r=e.distance,d=e.times||5,a=2*d+(p||s?1:0),h=e.duration/a,m=e.easing,y="up"===u||"down"===u?"top":"left",l="up"===u||"left"===u,e=c.queue(),u=e.length;for((p||s)&&n.push("opacity"),g.effects.save(c,n),c.show(),g.effects.createWrapper(c),r=r||c["top"==y?"outerHeight":"outerWidth"]()/3,p&&((f={opacity:1})[y]=0,c.css("opacity",0).css(y,l?2*-r:2*r).animate(f,h,m)),s&&(r/=Math.pow(2,d-1)),i=(f={})[y]=0;i<d;i++)(o={})[y]=(l?"-=":"+=")+r,c.animate(o,h,m).animate(f,h,m),r=s?2*r:r/2;s&&((o={opacity:0})[y]=(l?"-=":"+=")+r,c.animate(o,h,m)),c.queue(function(){s&&c.hide(),g.effects.restore(c,n),g.effects.removeWrapper(c),t()}),1<u&&e.splice.apply(e,[1,0].concat(e.splice(u,1+a))),c.dequeue()}});