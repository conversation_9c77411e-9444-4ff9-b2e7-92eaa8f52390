<?php get_header(); ?>
<?php while( have_posts() ): the_post(); $p_id = get_the_ID(); ?>
	<div class="module-default">
		<div class="module-inner">
				<div class="slideshow slideshow-min carousel clearfix" style="height:250px; overflow:hidden;">
					<div id="carousel-127389">
						<div class="carousel-item">
							<div class="carousel-img">
								<img src="<?php if( get_option('wenzhangbeijingtu') ) { ?><?php echo get_option( 'wenzhangbeijingtu' ); ?><?php }else{ ?><?php bloginfo('template_directory'); ?>/images/icon/wenzhang.png<?php }?>">
							</div>
						</div>
					</div>
				</div>
		</div>
	</div> 
	<section class="main">
		<div class="breadcrumbs-wrapper">
			<div class="page-width clearfix">
				<div class="breadcrumbs" skinobjectzone="HtmlBreadCrumb_2618">
					<span>当前位置：</span><?php if (function_exists('get_breadcrumbs')){get_breadcrumbs(); } ?>
				</div>
			</div>
		</div>
		<div class="page-width clearfix">
			<section class="content float-right">
				<div class="module-default">
					<div class="module-inner">
						<div class="article-detail">
						<div class="article-title">
							<h1><?php the_title_attribute(); ?></h1>
						</div>
						<div class="entry-meta">
                        <span>
                            <strong>所属分类：</strong>
                            <?php the_category(', ') ?>
						</span>
                        <span>
                            <strong>发布时间：</strong>
                            <strong><?php the_time('Y-m-d') ?></strong>
						</span>
						</div>
						<div class="article-content-wrapper">
							<div class="article-content">
								<div class="qhd-content" id="wzzt">
									<?php the_content(); ?>
								</div>
							</div>
							<?php endwhile; ?>
							<div class="detail-bottom">
                            <div class="float-right detail-bottom-button">
							<?php
								$prev_post = get_previous_post();
								if(!empty($prev_post)):?>
									<a title="<?php echo $prev_post->post_title;?>" href="<?php echo get_permalink($prev_post->ID);?>">上一篇</a>
							<?php endif;?>
							<?php
								$next_post = get_next_post();
								if(!empty($next_post)):?>
									<a title="<?php echo $next_post->post_title;?>" href="<?php echo get_permalink($next_post->ID);?>">下一篇</a>
							<?php endif;?>
							</div>
                            <div class="share-toolbar float-left clearfix">
                                <div class="bdsharebuttonbox bdshare-button-style0-16">
                                    <span style="float:left; height:16px; line-height:16px; margin:6px 0 6px 0;">分享到：</span>
                                    <a title="分享到QQ空间" href="#" class="bds_qzone" data-cmd="qzone"></a>
                                    <a title="分享到新浪微博" href="#" class="bds_tsina" data-cmd="tsina"></a>
                                    <a title="分享到腾讯微博" href="#" class="bds_tqq" data-cmd="tqq"></a>
                                    <a title="分享到人人网" href="#" class="bds_renren" data-cmd="renren"></a>
                                    <a title="分享到微信" href="#" class="bds_weixin" data-cmd="weixin"></a>
                                    <a href="#" class="bds_more" data-cmd="more"></a>
                                </div>
                                <script>window._bd_share_config = {
                                        "common": {
                                            "bdSnsKey": {},
                                            "bdText": "",
                                            "bdMini": "2",
                                            "bdMiniList": false,
                                            "bdPic": "",
                                            "bdStyle": "0",
                                            "bdSize": "16"
                                        },
                                        "share": {}
                                    };
                                    with(document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = 'http://bdimg.share.baidu.com/static/api/js/share.js?v=89860593.js?cdnversion=' + ~ ( - new Date() / 36e5)];</script>
							</div>
							</div>
						</div>
						</div>
					</div>
				</div>
			</section>
			<?php get_sidebar();?>
		</div>
	</section>
<?php get_footer(); ?>