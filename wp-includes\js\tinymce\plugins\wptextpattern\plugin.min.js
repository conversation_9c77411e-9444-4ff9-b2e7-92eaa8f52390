!function(u,m){function p(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}u.Env.ie&&u.Env.ie<9||u.PluginManager.add("wptextpattern",function(s){var f,t=u.util.VK,e=s.settings.wptextpattern||{},o=e.space||[{regExp:/^[*-]\s/,cmd:"InsertUnorderedList"},{regExp:/^1[.)]\s/,cmd:"InsertOrderedList"}],d=e.enter||[{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:">",format:"blockquote"},{regExp:/^(-){3,}$/,element:"hr"}],g=e.inline||[{delimiter:"`",format:"code"}];function n(){var r,i,o,e,t,d,l,n=s.selection.getRng(),a=n.startContainer,c=n.startOffset;a&&3===a.nodeType&&a.data.length&&c&&(d=a.data.slice(0,c),l=a.data.charAt(c-1),u.each(g,function(e){if(l===e.delimiter.slice(-1)){var t=p(e.delimiter),n=e.delimiter.charAt(0),a=new RegExp("(.*)"+t+".+"+t+"$"),t=d.match(a);if(t){r=t[1].length,i=c-e.delimiter.length;a=d.charAt(r-1),t=d.charAt(r+e.delimiter.length);if(!(r&&/\S/.test(a)&&(/\s/.test(t)||a===n)||new RegExp("^[\\s"+p(n)+"]+$").test(d.slice(r,i))))return o=e,!1}}}),o&&(e=s.formatter.get(o.format))&&e[0].inline&&(s.undoManager.add(),s.undoManager.transact(function(){a.insertData(c,"\ufeff"),a=a.splitText(r),t=a.splitText(c-r),a.deleteData(0,o.delimiter.length),a.deleteData(a.data.length-o.delimiter.length,o.delimiter.length),s.formatter.apply(o.format,{},a),s.selection.setCursorLocation(t,1)}),m(function(){f="space",s.once("selectionchange",function(){var e;t&&-1!==(e=t.data.indexOf("\ufeff"))&&t.deleteData(e,e+1)})})))}function l(e){var t,n=s.dom.getParent(e,"p");if(n){for(;(t=n.firstChild)&&3!==t.nodeType;)n=t;if(t)return t=!t.data?t.nextSibling&&3===t.nextSibling.nodeType?t.nextSibling:null:t}}function a(){var n,a,r=s.selection.getRng(),i=r.startContainer;i&&l(i)===i&&(n=i.parentNode,a=i.data,u.each(o,function(e){var t=a.match(e.regExp);if(t&&r.startOffset===t[0].length)return s.undoManager.add(),s.undoManager.transact(function(){i.deleteData(0,t[0].length),n.innerHTML||n.appendChild(document.createElement("br")),s.selection.setCursorLocation(n),s.execCommand(e.cmd)}),m(function(){f="space"}),!1}))}s.on("selectionchange",function(){f=null}),s.on("keydown",function(e){(f&&27===e.keyCode||"space"===f&&e.keyCode===t.BACKSPACE)&&(s.undoManager.undo(),e.preventDefault(),e.stopImmediatePropagation()),t.metaKeyPressed(e)||(e.keyCode===t.ENTER?function(){var e,t,n,a=s.selection.getRng().startContainer,r=l(a),i=d.length;if(r){for(e=r.data;i--;)if(d[i].start){if(0===e.indexOf(d[i].start)){t=d[i];break}}else if(d[i].regExp&&d[i].regExp.test(e)){t=d[i];break}t&&(r===a&&u.trim(e)===t.start||s.once("keyup",function(){s.undoManager.add(),s.undoManager.transact(function(){var e;t.format?(s.formatter.apply(t.format,{},r),r.replaceData(0,r.data.length,(e=r.data.slice(t.start.length))?e.replace(/^\s+/,""):"")):t.element&&(n=r.parentNode&&r.parentNode.parentNode)&&n.replaceChild(document.createElement(t.element),r.parentNode)}),m(function(){f="enter"})}))}}():e.keyCode===t.SPACEBAR?m(a):47<e.keyCode&&!(91<=e.keyCode&&e.keyCode<=93)&&m(n))},!0)})}(window.tinymce,window.setTimeout);