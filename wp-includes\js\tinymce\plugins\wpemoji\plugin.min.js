!function(d,c,m){d.PluginManager.add("wpemoji",function(n){var t,i=d.Env,e=window.navigator.userAgent,o=-1<e.indexOf("Windows"),e=!!((e=e.match(/Windows NT 6\.(\d)/))&&1<e[1]);function a(e){c.emoji.parse(e,{imgAttr:{"data-mce-resize":"false","data-mce-placeholder":"1","data-wp-emoji":"1"}})}function w(e){var t,o;e&&window.twemoji&&window.twemoji.test(e.textContent||e.innerText)&&(i.webkit&&(o=(t=n.selection).getBookmark()),a(e),i.webkit&&t.moveToBookmark(o))}c&&c.emoji&&!m.supports.everything&&(e?n.on("keyup",function(e){231===e.keyCode&&w(n.selection.getNode())}):o||(n.on("keydown keyup",function(e){t="keydown"===e.type}),n.on("input",function(){t||w(n.selection.getNode())})),n.on("setcontent",function(e){var t=n.selection,o=t.getNode();window.twemoji&&window.twemoji.test(o.textContent||o.innerText)&&(a(o),i.ie&&i.ie<9&&e.load&&o&&"BODY"===o.nodeName&&t.collapse(!0))}),n.on("PastePostProcess",function(e){window.twemoji&&d.each(n.dom.$("img.emoji",e.node),function(e){e.alt&&window.twemoji.test(e.alt)&&((e=e).className="emoji",e.setAttribute("data-mce-resize","false"),e.setAttribute("data-mce-placeholder","1"),e.setAttribute("data-wp-emoji","1"))})}),n.on("postprocess",function(e){e.content&&(e.content=e.content.replace(/<img[^>]+data-wp-emoji="[^>]+>/g,function(e){var t=e.match(/alt="([^"]+)"/);return t&&t[1]?t[1]:e}))}),n.on("resolvename",function(e){"IMG"===e.target.nodeName&&n.dom.getAttrib(e.target,"data-wp-emoji")&&e.preventDefault()}))})}(window.tinymce,window.wp,window._wpemojiSettings);