var a1portal;var A1PORTAL_HIGHLIGHT_COLOR="#000000";var a1portal_MOVEMODE=false;var COL_DELIMITER=String.fromCharCode(18);var ROW_DELIMITER=String.fromCharCode(17);var QUOTE_REPLACEMENT=String.fromCharCode(19);var KEY_LEFT_ARROW=37;var KEY_UP_ARROW=38;var KEY_RIGHT_ARROW=39;var KEY_DOWN_ARROW=40;var KEY_RETURN=13;var KEY_ESCAPE=27;if(typeof(__a1portal_m_aNamespaces)=="undefined"){var __a1portal_m_aNamespaces=new Array()}function __a1portal(){this.apiversion=0.4;this.pns="";this.ns="a1portal";this.diagnostics=null;this.vars=null;this.dependencies=new Array();this.isLoaded=false;this.delay=new Array()}__a1portal.prototype={getVars:function(){if(this.vars==null){this.vars=new Array();var c=a1portal.dom.getById("__a1portalVariable");if(c!=null){if(c.value.indexOf("__scdoff")!=-1){COL_DELIMITER="~|~";ROW_DELIMITER="~`~";QUOTE_REPLACEMENT="~!~"}var d=c.value.split(ROW_DELIMITER);for(var b=0;b<d.length;b++){var a=d[b].split(COL_DELIMITER);if(a.length==2){this.vars[a[0]]=a[1]}}}}return this.vars},getVar:function(sKey){if(this.getVars()[sKey]!=null){var re=eval("/"+QUOTE_REPLACEMENT+"/g");return this.getVars()[sKey].replace(re,'"')}},setVar:function(sKey,sVal){if(this.vars==null){this.getVars()}this.vars[sKey]=sVal;var oCtl=a1portal.dom.getById("__a1portalVariable");if(oCtl==null){oCtl=a1portal.dom.createElement("INPUT");oCtl.type="hidden";oCtl.id="__a1portalVariable";a1portal.dom.appendChild(a1portal.dom.getByTagName("body")[0],oCtl)}var sVals="";var s;var re=eval('/"/g');for(s in this.vars){sVals+=ROW_DELIMITER+s+COL_DELIMITER+this.vars[s].toString().replace(re,QUOTE_REPLACEMENT)}oCtl.value=sVals;return true},callPostBack:function(sAction){var sPostBack=a1portal.getVar("__a1portal_postBack");var sData="";if(sPostBack.length>0){sData+=sAction;for(var i=1;i<arguments.length;i++){var aryParam=arguments[i].split("=");sData+=COL_DELIMITER+aryParam[0]+COL_DELIMITER+aryParam[1]}eval(sPostBack.replace("[DATA]",sData));return true}return false},createDelegate:function(b,a){return function(){a.apply(b,arguments)}},doDelay:function(d,c,b,a){if(this.delay[d]==null){this.delay[d]=new a1portal.delayObject(b,a,d);this.delay[d].num=window.setTimeout(a1portal.createDelegate(this.delay[d],this.delay[d].complete),c)}},cancelDelay:function(a){if(this.delay[a]!=null){window.clearTimeout(this.delay[a].num);this.delay[a]=null}},decodeHTML:function(a){return a.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"')},encode:function(a){if(encodeURIComponent){return encodeURIComponent(a)}else{return escape(a)}},encodeHTML:function(a){return a.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/\"/g,"&quot;")},evalJSON:function(s){return eval("("+s+")")},escapeForEval:function(a){return a.replace(/\\/g,"\\\\").replace(/\'/g,"\\'").replace(/\r/g,"").replace(/\n/g,"\\n").replace(/\./,"\\.")},extend:function(a,b){for(s in b){a[s]=b[s]}return a},dependenciesLoaded:function(){return true},loadNamespace:function(){if(this.isLoaded==false){if(this.dependenciesLoaded()){a1portal=this;this.isLoaded=true;this.loadDependencies(this.pns,this.ns)}}},loadDependencies:function(a,d){for(var b=0;b<__a1portal_m_aNamespaces.length;b++){for(var c=0;c<__a1portal_m_aNamespaces[b].dependencies.length;c++){if(__a1portal_m_aNamespaces[b].dependencies[c]==a+(a.length>0?".":"")+d){__a1portal_m_aNamespaces[b].loadNamespace()}}}}};__a1portal.prototype.delayObject=function(b,a,c){this.num=null;this.pfunc=b;this.context=a;this.type=c};__a1portal.prototype.delayObject.prototype={complete:function(){a1portal.delay[this.type]=null;this.pfunc(this.context)}};__a1portal.prototype.ScriptRequest=function(a,b,c){this.ctl=null;this.xmlhttp=null;this.src=null;this.text=null;if(a!=null&&a.length>0){this.src=a}if(b!=null&&b.length>0){this.text=b}this.callBack=c;this.status="init";this.timeOut=5000};__a1portal.prototype.ScriptRequest.prototype={load:function(){this.status="loading";this.ctl=document.createElement("script");this.ctl.type="text/javascript";if(this.src!=null){if(a1portal.dom.browser.isType(a1portal.dom.browser.Safari)){this.xmlhttp=new XMLHttpRequest();this.xmlhttp.open("GET",this.src,true);this.xmlhttp.onreadystatechange=a1portal.createDelegate(this,this.xmlhttpStatusChange);this.xmlhttp.send(null);return}else{if(a1portal.dom.browser.isType(a1portal.dom.browser.InternetExplorer)){this.ctl.onreadystatechange=a1portal.createDelegate(this,this.statusChange)}else{if(a1portal.dom.browser.isType(a1portal.dom.browser.Opera)==false){this.ctl.onload=a1portal.createDelegate(this,this.complete)}}this.ctl.src=this.src}}else{if(a1portal.dom.browser.isType(a1portal.dom.browser.Safari)){this.ctl.innerHTML=a1portal.encodeHTML(this.text)}else{this.ctl.text=this.text}}var a=a1portal.dom.getByTagName("HEAD");if(a){if(a1portal.dom.browser.isType(a1portal.dom.browser.Opera)==false||this.src!=null){a[0].appendChild(this.ctl)}}else{alert("Cannot load dynamic script, no HEAD tag present.")}if(this.src==null||a1portal.dom.browser.isType(a1portal.dom.browser.Opera)){this.complete()}else{if(this.timeOut){a1portal.doDelay("loadScript_"+this.src,this.timeOut,a1portal.createDelegate(this,this.reload),null)}}},xmlhttpStatusChange:function(){if(this.xmlhttp.readyState!=4){return}this.src=null;this.text=this.xmlhttp.responseText;this.load()},statusChange:function(){if((this.ctl.readyState=="loaded"||this.ctl.readyState=="complete")&&this.status!="complete"){this.complete()}},reload:function(){if(a1portal.dom.scriptStatus(this.src)=="complete"){this.complete()}else{this.load()}},complete:function(){a1portal.cancelDelay("loadScript_"+this.src);this.status="complete";if(typeof(this.callBack)!="undefined"){this.callBack(this)}this.dispose()},dispose:function(){this.callBack=null;if(this.ctl){if(this.ctl.onreadystatechange){this.ctl.onreadystatechange=new function(){}}else{if(this.ctl.onload){this.ctl.onload=null}}this.ctl=null}this.xmlhttp=null}};function a1portal_dom(){this.pns="a1portal";this.ns="dom";this.dependencies="a1portal".split(",");this.isLoaded=false;this.browser=new this.browserObject();this.__leakEvts=new Array();this.scripts=[];this.scriptElements=[]}a1portal_dom.prototype={insertAfter:function(c,a){var b=a.parentNode;if(b.lastChild==a){b.appendChild(c)}else{b.insertBefore(c,a.nextSibling)}},appendChild:function(b,a){if(b.appendChild){return b.appendChild(a)}else{return null}},attachEvent:function(a,d,b){if(a1portal.dom.browser.isType(a1portal.dom.browser.InternetExplorer)==false){var c=d.substring(2);a.addEventListener(c,function(e){a1portal.dom.event=new a1portal.dom.eventObject(e,e.target,this);return b()},false)}else{a.attachEvent(d,function(){a1portal.dom.event=new a1portal.dom.eventObject(window.event,window.event.srcElement,this);return b()})}return true},destroyEvent:function(a,c,b){if(window.addEventListener){a.removeEventListener(c,b,true)}else{a.detachEvent(c,b)}},createElement:function(a){if(document.createElement){return document.createElement(a.toLowerCase())}else{return null}},cursorPos:function(f){if(f.value.length==0){return 0}var h=-1;if(f.selectionStart){h=f.selectionStart}else{if(f.createTextRange){var a=window.document.selection.createRange();var g=f.createTextRange();if(g==null||a==null||((a.text!="")&&g.inRange(a)==false)){return -1}if(a.text==""){if(g.boundingLeft==a.boundingLeft){h=0}else{var b=f.tagName.toLowerCase();if(b=="input"){var c=g.text;var d=1;while(d<c.length){g.findText(c.substring(d));if(g.boundingLeft==a.boundingLeft){break}d++}}else{if(b=="textarea"){var d=f.value.length+1;var e=document.selection.createRange().duplicate();while(e.parentElement()==f&&e.move("character",1)==1){--d}if(d==f.value.length+1){d=-1}}}h=d}}else{h=g.text.indexOf(a.text)}}}return h},cancelCollapseElement:function(a){a1portal.cancelDelay(a.id+"col");a.style.display="none"},collapseElement:function(c,a,d){if(a==null){a=10}c.style.overflow="hidden";var b=new Object();b.num=a;b.ctl=c;b.pfunc=d;c.origHeight=c.offsetHeight;a1portal.dom.__collapseElement(b)},__collapseElement:function(b){var a=b.num;var d=b.ctl;var c=d.origHeight/a;if(d.offsetHeight-(c*2)>0){d.style.height=(d.offsetHeight-c).toString()+"px";a1portal.doDelay(d.id+"col",10,a1portal.dom.__collapseElement,b)}else{d.style.display="none";if(b.pfunc!=null){b.pfunc()}}},cancelExpandElement:function(a){a1portal.cancelDelay(a.id+"exp");a.style.overflow="";a.style.height=""},expandElement:function(c,a,d){if(a==null){a=10}if(c.style.display=="none"&&c.origHeight==null){c.style.display="";c.style.overflow="";c.origHeight=c.offsetHeight;c.style.overflow="hidden";c.style.height="1px"}c.style.display="";var b=new Object();b.num=a;b.ctl=c;b.pfunc=d;a1portal.dom.__expandElement(b)},__expandElement:function(b){var a=b.num;var d=b.ctl;var c=d.origHeight/a;if(d.offsetHeight+c<d.origHeight){d.style.height=(d.offsetHeight+c).toString()+"px";a1portal.doDelay(d.id+"exp",10,a1portal.dom.__expandElement,b)}else{d.style.overflow="";d.style.height="";if(b.pfunc!=null){b.pfunc()}}},deleteCookie:function(c,b,a){if(this.getCookie(c)){this.setCookie(c,"",-1,b,a);return true}return false},getAttr:function(c,b,d){if(c.getAttribute==null){return d}var a=c.getAttribute(b);if(a==null||a==""){return d}else{return a}},getById:function(a,b){if(b==null){b=document}if(b.getElementById){return b.getElementById(a)}else{if(b.all){return b.all(a)}else{return null}}},getByTagName:function(a,b){if(b==null){b=document}if(b.getElementsByTagName){return b.getElementsByTagName(a)}else{if(b.all&&b.all.tags){return b.all.tags(a)}else{return null}}},getParentByTagName:function(b,a){var c=b.parentNode;a=a.toLowerCase();while(c!=null){if(c.tagName&&c.tagName.toLowerCase()==a){return c}c=c.parentNode}return null},getCookie:function(f){var e=" "+document.cookie;var d=" "+f+"=";var b=null;var c=0;var a=0;if(e.length>0){c=e.indexOf(d);if(c!=-1){c+=d.length;a=e.indexOf(";",c);if(a==-1){a=e.length}b=unescape(e.substring(c,a))}}return(b)},getNonTextNode:function(a){if(this.isNonTextNode(a)){return a}while(a!=null&&this.isNonTextNode(a)){a=this.getSibling(a,1)}return a},__leakEvt:function(c,b,a){this.name=c;this.ctl=b;this.ptr=a},addSafeHandler:function(c,d,a,b){c[d]=this.getObjMethRef(a,b);if(a1portal.dom.browser.isType(a1portal.dom.browser.InternetExplorer)){if(this.__leakEvts.length==0){a1portal.dom.attachEvent(window,"onunload",a1portal.dom.destroyHandlers)}this.__leakEvts[this.__leakEvts.length]=new a1portal.dom.__leakEvt(d,c,c[d])}},destroyHandlers:function(){var c=a1portal.dom.__leakEvts.length-1;for(var a=c;a>=0;a--){var b=a1portal.dom.__leakEvts[a];b.ctl.detachEvent(b.name,b.ptr);b.ctl[b.name]=null;a1portal.dom.__leakEvts.length=a1portal.dom.__leakEvts.length-1}},getObjMethRef:function(b,a){return(function(c){c=c||window.event;return b[a](c,this)})},getScript:function(b){if(this.scriptElements[b]){return this.scriptElements[b]}var a=a1portal.dom.getByTagName("SCRIPT");for(var c=0;c<a.length;c++){if(a[c].src!=null&&a[c].src.indexOf(b)>-1){this.scriptElements[b]=a[c];return a[c]}}},getScriptPath:function(){var a=a1portal.dom.getScript("a1portal.js");if(a){return a.src.replace("a1portal.js","")}return""},getSibling:function(b,c){if(b!=null&&b.parentNode!=null){for(var a=0;a<b.parentNode.childNodes.length;a++){if(b.parentNode.childNodes[a].id==b.id){if(b.parentNode.childNodes[a+c]!=null){return b.parentNode.childNodes[a+c]}}}}return null},isNonTextNode:function(a){return(a.nodeType!=3&&a.nodeType!=8)},scriptFile:function(a){var b=a.split("/");return b[b.length-1]},loadScript:function(a,c,d){var e;if(a!=null&&a.length>0){e=this.scriptFile(a);if(this.scripts[e]!=null){return}}var b=new a1portal.ScriptRequest(a,c,d);if(e){this.scripts[e]=b}b.load();return b},loadScripts:function(a,b,c){if(a1portal.scripts==null){var e=function(f,g,h){return(function(){a1portal.dom.loadScripts(f,g,h)})};a1portal.dom.loadScript(a1portal.dom.getScriptPath()+"a1portal.scripts.js",null,e(a,b,c));return}var d=new a1portal.scripts.ScriptBatchRequest(a,b,c);d.load()},scriptStatus:function(a){var c=this.scriptFile(a);if(this.scripts[c]){return this.scripts[c].status}var b=this.getScript(a);if(b!=null){return"complete"}else{return""}},setScriptLoaded:function(a){var b=this.scriptFile(a);if(this.scripts[b]&&a1portal.dom.scripts[b].status!="complete"){a1portal.dom.scripts[b].complete()}},navigate:function(b,a){if(a!=null&&a.length>0){if(a=="_blank"){window.open(b)}else{document.frames[a].location.href=b}}else{window.location.href=b}return false},removeChild:function(a){if(a.parentNode){return a.parentNode.removeChild(a)}else{return null}},setCookie:function(e,b,g,c,a,d){var f;if(g){f=new Date();f.setTime(f.getTime()+(g*24*60*60*1000))}document.cookie=e+"="+escape(b)+((f)?"; expires="+f.toGMTString():"")+((c)?"; path="+c:"")+((a)?"; domain="+a:"")+((d)?"; secure":"");if(document.cookie.length>0){return true}},getCurrentStyle:function(a,b){if(document.defaultView){if(a.nodeType!=a.ELEMENT_NODE){return null}return document.defaultView.getComputedStyle(a,"").getPropertyValue(b.split("-").join(""))}if(a.currentStyle){return a.currentStyle[b.split("-").join("")]}if(a.style){return a.style.getAttribute(b.split("-").join(""))}return null},dependenciesLoaded:function(){return(typeof(a1portal)!="undefined")},loadNamespace:function(){if(this.isLoaded==false){if(this.dependenciesLoaded()){a1portal.dom=this;this.isLoaded=true;a1portal.loadDependencies(this.pns,this.ns)}}},getFormPostString:function(c){var b="";if(c!=null){if(c.tagName&&c.tagName.toLowerCase()=="form"){for(var a=0;a<c.elements.length;a++){b+=this.getElementPostString(c.elements[a])}}else{b=this.getElementPostString(c);for(var a=0;a<c.childNodes.length;a++){b+=this.getFormPostString(c.childNodes[a])}}}return b},getElementPostString:function(c){var a;if(c.tagName){a=c.tagName.toLowerCase()}if(a=="input"){var d=c.type.toLowerCase();if(d=="text"||d=="password"||d=="hidden"||((d=="checkbox"||d=="radio")&&c.checked)){return c.name+"="+a1portal.encode(c.value)+"&"}}else{if(a=="select"){for(var b=0;b<c.options.length;b++){if(c.options[b].selected){return c.name+"="+a1portal.encode(c.options[b].value)+"&"}}}else{if(a=="textarea"){return c.name+"="+a1portal.encode(c.value)+"&"}}}return""}};a1portal_dom.prototype.eventObject=function(c,a,b){this.object=c;this.srcElement=a;this.currentTarget=b};a1portal_dom.prototype.browserObject=function(){this.InternetExplorer="ie";this.Netscape="ns";this.Mozilla="mo";this.Opera="op";this.Safari="safari";this.Konqueror="kq";this.MacIE="macie";var d;var c=navigator.userAgent.toLowerCase();if(c.indexOf("konqueror")!=-1){d=this.Konqueror}else{if(c.indexOf("opera")!=-1){d=this.Opera}else{if(c.indexOf("netscape")!=-1){d=this.Netscape}else{if(c.indexOf("msie")!=-1){if(c.indexOf("mac")!=-1){d=this.MacIE}else{d=this.InternetExplorer}}else{if(c.indexOf("safari")!=-1){d="safari"}}}}}if(d==null){d=this.Mozilla}this.type=d;this.version=parseFloat(navigator.appVersion);var b=navigator.userAgent.toLowerCase();if(this.type==this.InternetExplorer){var a=navigator.appVersion.split("MSIE");this.version=parseFloat(a[1])}if(this.type==this.Netscape){var a=b.split("netscape");this.version=parseFloat(a[1].split("/")[1])}};a1portal_dom.prototype.browserObject.prototype={toString:function(){return this.type+" "+this.version},isType:function(){for(var a=0;a<arguments.length;a++){if(a1portal.dom.browser.type==arguments[a]){return true}}return false}};function a1portal_controls(){this.pns="a1portal";this.ns="controls";this.dependencies="a1portal,a1portal.dom".split(",");this.isLoaded=false;this.controls=new Array();this.toolbars=[];this.orient=new Object();this.orient.horizontal=0;this.orient.vertical=1;this.action=new Object();this.action.postback=0;this.action.expand=1;this.action.none=2;this.action.nav=3}a1portal_controls.prototype={dependenciesLoaded:function(){return(typeof(a1portal)!="undefined"&&typeof(a1portal.dom)!="undefined")},loadNamespace:function(){if(this.isLoaded==false){if(this.dependenciesLoaded()){if(typeof(a1portal_control)!="undefined"){a1portal.extend(a1portal_controls.prototype,new a1portal_control)}a1portal.controls=new a1portal_controls();this.isLoaded=true;a1portal.loadDependencies(this.pns,this.ns)}}}};a1portal_controls.prototype.a1portalNode=function(a){if(a!=null){this.node=a;this.id=a.getAttribute("id","");this.key=a.getAttribute("key","");this.text=a.getAttribute("txt","");this.url=a.getAttribute("url","");this.js=a.getAttribute("js","");this.target=a.getAttribute("tar","");this.toolTip=a.getAttribute("tTip","");this.enabled=a.getAttribute("enabled","1")!="0";this.css=a.getAttribute("css","");this.cssSel=a.getAttribute("cssSel","");this.cssHover=a.getAttribute("cssHover","");this.cssIcon=a.getAttribute("cssIcon","");this.hasNodes=a.childNodeCount()>0;this.hasPendingNodes=(a.getAttribute("hasNodes","0")=="1"&&this.hasNodes==false);this.imageIndex=new Number(a.getAttribute("imgIdx","-1"));this.image=a.getAttribute("img","");this.level=this.getNodeLevel()}};a1portal_controls.prototype.a1portalNode.prototype={childNodeCount:function(){return this.node.childNodes.length},getNodeLevel:function(){var a=0;var b=this.node;while(b!=null){b=b.parentNode();if(b==null||b.nodeName()=="root"){break}a++}return a},update:function(a){if(a!=null){var b=typeof(this[a]);if(b=="string"||b=="number"||this[a]==null){this.node.setAttribute(a,this[a])}else{if(b=="boolean"){this.node.setAttribute(a,new Number(this[a]))}}}else{for(a in this){this.update(a)}}}};function a1portal_util(){this.pns="a1portal";this.ns="utilities";this.dependencies="a1portal,a1portal.dom".split(",");this.isLoaded=false}a1portal_util.prototype.dependenciesLoaded=function(){return(typeof(a1portal)!="undefined"&&typeof(a1portal.dom)!="undefined")};a1portal_util.prototype.loadNamespace=function(){if(this.isLoaded==false){if(this.dependenciesLoaded()){if(typeof(a1portal_utility)!="undefined"){a1portal.extend(a1portal_util.prototype,new a1portal_utility)}a1portal.util=new a1portal_util();this.isLoaded=true;a1portal.loadDependencies(this.pns,this.ns)}}};__a1portal_m_aNamespaces[__a1portal_m_aNamespaces.length]=new a1portal_util();__a1portal_m_aNamespaces[__a1portal_m_aNamespaces.length]=new a1portal_controls();__a1portal_m_aNamespaces[__a1portal_m_aNamespaces.length]=new a1portal_dom();__a1portal_m_aNamespaces[__a1portal_m_aNamespaces.length]=new __a1portal();for(var i=__a1portal_m_aNamespaces.length-1;i>=0;i--){__a1portal_m_aNamespaces[i].loadNamespace()};