!function(i){var a=i.customize;a.HeaderTool={},a.<PERSON>erTool.ImageModel=Backbone.Model.extend({defaults:function(){return{header:{attachment_id:0,url:"",timestamp:_.now(),thumbnail_url:""},choice:"",selected:!1,random:!1}},initialize:function(){this.on("hide",this.hide,this)},hide:function(){this.set("choice",""),a("header_image").set("remove-header"),a("header_image_data").set("remove-header")},destroy:function(){var e=this.get("header"),t=a.HeaderTool.currentHeader.get("header").attachment_id;t&&e.attachment_id===t&&a.HeaderTool.currentHeader.trigger("hide"),i.ajax.post("custom-header-remove",{nonce:_wpCustomizeHeader.nonces.remove,wp_customize:"on",theme:a.settings.theme.stylesheet,attachment_id:e.attachment_id}),this.trigger("destroy",this,this.collection)},save:function(){this.get("random")?(a("header_image").set(this.get("header").random),a("header_image_data").set(this.get("header").random)):this.get("header").defaultName?(a("header_image").set(this.get("header").url),a("header_image_data").set(this.get("header").defaultName)):(a("header_image").set(this.get("header").url),a("header_image_data").set(this.get("header"))),a.HeaderTool.combinedList.trigger("control:setImage",this)},importImage:function(){var e=this.get("header");void 0!==e.attachment_id&&i.ajax.post("custom-header-add",{nonce:_wpCustomizeHeader.nonces.add,wp_customize:"on",theme:a.settings.theme.stylesheet,attachment_id:e.attachment_id})},shouldBeCropped:function(){return(!0!==this.get("themeFlexWidth")||!0!==this.get("themeFlexHeight"))&&((!0!==this.get("themeFlexWidth")||this.get("themeHeight")!==this.get("imageHeight"))&&((!0!==this.get("themeFlexHeight")||this.get("themeWidth")!==this.get("imageWidth"))&&((this.get("themeWidth")!==this.get("imageWidth")||this.get("themeHeight")!==this.get("imageHeight"))&&!(this.get("imageWidth")<=this.get("themeWidth")))))}}),a.HeaderTool.ChoiceList=Backbone.Collection.extend({model:a.HeaderTool.ImageModel,comparator:function(e){return-e.get("header").timestamp},initialize:function(){var i=a.HeaderTool.currentHeader.get("choice").replace(/^https?:\/\//,""),e=this.isRandomChoice(a.get().header_image);this.type||(this.type="uploaded"),void 0===this.data&&(this.data=_wpCustomizeHeader.uploads),e&&(i=a.get().header_image),this.on("control:setImage",this.setImage,this),this.on("control:removeImage",this.removeImage,this),this.on("add",this.maybeAddRandomChoice,this),_.each(this.data,function(e,t){e.attachment_id||(e.defaultName=t),void 0===e.timestamp&&(e.timestamp=0),this.add({header:e,choice:e.url.split("/").pop(),selected:i===e.url.replace(/^https?:\/\//,"")},{silent:!0})},this),0<this.size()&&this.addRandomChoice(i)},maybeAddRandomChoice:function(){1===this.size()&&this.addRandomChoice()},addRandomChoice:function(e){var t=RegExp(this.type).test(e),e="random-"+this.type+"-image";this.add({header:{timestamp:0,random:e,width:245,height:41},choice:e,random:!0,selected:t})},isRandomChoice:function(e){return/^random-(uploaded|default)-image$/.test(e)},shouldHideTitle:function(){return this.size()<2},setImage:function(e){this.each(function(e){e.set("selected",!1)}),e&&e.set("selected",!0)},removeImage:function(){this.each(function(e){e.set("selected",!1)})}}),a.HeaderTool.DefaultsList=a.HeaderTool.ChoiceList.extend({initialize:function(){this.type="default",this.data=_wpCustomizeHeader.defaults,a.HeaderTool.ChoiceList.prototype.initialize.apply(this)}})}((jQuery,window.wp));