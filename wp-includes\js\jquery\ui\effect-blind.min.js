/*!
 * jQuery UI Effects Blind 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/blind-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(l){return l.effects.effect.blind=function(e,t){var s,i,o=l(this),f=["position","top","bottom","left","right","height","width"],n=l.effects.setMode(o,e.mode||"hide"),c=e.direction||"up",r=/up|down|vertical/.test(c),a=r?"height":"width",p=r?"top":"left",d=/up|left|vertical|horizontal/.test(c),u={},h="show"===n;o.parent().is(".ui-effects-wrapper")?l.effects.save(o.parent(),f):l.effects.save(o,f),o.show(),i=(s=l.effects.createWrapper(o).css({overflow:"hidden"}))[a](),c=parseFloat(s.css(p))||0,u[a]=h?i:0,d||(o.css(r?"bottom":"right",0).css(r?"top":"left","auto").css({position:"absolute"}),u[p]=h?c:i+c),h&&(s.css(a,0),d||s.css(p,c+i)),s.animate(u,{duration:e.duration,easing:e.easing,queue:!1,complete:function(){"hide"===n&&o.hide(),l.effects.restore(o,f),l.effects.removeWrapper(o),t()}})}});