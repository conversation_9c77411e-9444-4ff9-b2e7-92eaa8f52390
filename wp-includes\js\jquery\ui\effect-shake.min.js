/*!
 * jQuery UI Effects Shake 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/shake-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(q){return q.effects.effect.shake=function(e,t){var i,f=q(this),n=["position","top","bottom","left","right","height","width"],a=q.effects.setMode(f,e.mode||"effect"),o=e.direction||"left",s=e.distance||20,c=e.times||3,r=2*c+1,u=Math.round(e.duration/r),d="up"===o||"down"===o?"top":"left",p="up"===o||"left"===o,h={},m={},g={},l=f.queue(),o=l.length;for(q.effects.save(f,n),f.show(),q.effects.createWrapper(f),h[d]=(p?"-=":"+=")+s,m[d]=(p?"+=":"-=")+2*s,g[d]=(p?"-=":"+=")+2*s,f.animate(h,u,e.easing),i=1;i<c;i++)f.animate(m,u,e.easing).animate(g,u,e.easing);f.animate(m,u,e.easing).animate(h,u/2,e.easing).queue(function(){"hide"===a&&f.hide(),q.effects.restore(f,n),q.effects.removeWrapper(f),t()}),1<o&&l.splice.apply(l,[1,0].concat(l.splice(o,1+r))),f.dequeue()}});