<?php
            // $db = new mysqli("rm-bp17yzz3p774yx16n.mysql.rds.aliyuncs.com","rr4po6m97f","Hanpan110119","rr4po6m97f");
ini_set('date.timezone','Asia/Shanghai');
 
$con = mysql_connect("rm-bp17yzz3p774yx16n.mysql.rds.aliyuncs.com","rr4po6m97f","Hanpan110119");
if (!$con)
  {
  die('Could not connect: ' . mysql_error());
  }
mysql_query('SET NAMES UTF8');
mysql_select_db("rr4po6m97f", $con);
            $content = file_get_contents('php://input');
            $post    = json_decode($content, true);
            $code = $post["code"];
            $sql = "select * from report where project_code = '$code' order by id desc limit 1;";

if (!mysql_query($sql,$con))
  {
  die('Error: ' . mysql_error());
  }
  $r = mysql_query($sql,$con);

            // $r = $db->query($sql);
            
            $dataarr = array();
            if( mysql_num_rows( $r )){
              while($row = mysql_fetch_array($r)){
                $dataarr[]=$row;
                
            $arr = [
    "code" => $row[1],
    "projectName" => $row[2],
    "unit" => $row[3],
    "company" => $row[4],
    "item" => $row[5],
    "checkingOfType" => $row[6],
    "address" => $row[7],
    "date" => $row[8],
    "temperature" => $row[9],
    "humidity" => $row[10],
    "status" => $row[11],
    "conclusion" => $row[12]
];
              }
            Response::json(200,'数据返回成功',$arr);
            }else{
              Response::json(300,'无数据',null);
            }
            
            
          
          class Response{
        public static function json($code,$message="",$data=array()){
            $result=array(
              'code'=>$code,
              'message'=>$message,
              'data'=>$data 
            );
            //输出json
            echo json_encode($result);
            exit;
        }
    }
          ?>