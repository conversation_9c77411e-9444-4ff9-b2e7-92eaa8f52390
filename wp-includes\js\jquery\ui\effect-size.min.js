/*!
 * jQuery UI Effects Size 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/size-effect/
 */
!function(t){"function"==typeof define&&define.amd?define(["jquery","./effect"],t):t(jQuery)}(function(w){return w.effects.effect.size=function(r,t){var o,h,n=w(this),e=["position","top","bottom","left","right","width","height","overflow","opacity"],s=["width","height","overflow"],i=["fontSize"],c=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],a=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],f=w.effects.setMode(n,r.mode||"effect"),d=r.restore||"effect"!==f,m=r.scale||"both",g=r.origin||["middle","center"],u=n.css("position"),p=d?e:["position","top","bottom","left","right","overflow","opacity"],y={height:0,width:0,outerHeight:0,outerWidth:0};"show"===f&&n.show(),o={height:n.height(),width:n.width(),outerHeight:n.outerHeight(),outerWidth:n.outerWidth()},"toggle"===r.mode&&"show"===f?(n.from=r.to||y,n.to=r.from||o):(n.from=r.from||("show"===f?y:o),n.to=r.to||("hide"===f?y:o)),h={from:{y:n.from.height/o.height,x:n.from.width/o.width},to:{y:n.to.height/o.height,x:n.to.width/o.width}},"box"!==m&&"both"!==m||(h.from.y!==h.to.y&&(p=p.concat(c),n.from=w.effects.setTransition(n,c,h.from.y,n.from),n.to=w.effects.setTransition(n,c,h.to.y,n.to)),h.from.x!==h.to.x&&(p=p.concat(a),n.from=w.effects.setTransition(n,a,h.from.x,n.from),n.to=w.effects.setTransition(n,a,h.to.x,n.to))),"content"!==m&&"both"!==m||h.from.y!==h.to.y&&(p=p.concat(i).concat(s),n.from=w.effects.setTransition(n,i,h.from.y,n.from),n.to=w.effects.setTransition(n,i,h.to.y,n.to)),w.effects.save(n,p),n.show(),w.effects.createWrapper(n),n.css("overflow","hidden").css(n.from),g&&(g=w.effects.getBaseline(g,o),n.from.top=(o.outerHeight-n.outerHeight())*g.y,n.from.left=(o.outerWidth-n.outerWidth())*g.x,n.to.top=(o.outerHeight-n.to.outerHeight)*g.y,n.to.left=(o.outerWidth-n.to.outerWidth)*g.x),n.css(n.from),"content"!==m&&"both"!==m||(c=c.concat(["marginTop","marginBottom"]).concat(i),a=a.concat(["marginLeft","marginRight"]),s=e.concat(c).concat(a),n.find("*[width]").each(function(){var t=w(this),o=t.height(),e=t.width(),i=t.outerHeight(),f=t.outerWidth();d&&w.effects.save(t,s),t.from={height:o*h.from.y,width:e*h.from.x,outerHeight:i*h.from.y,outerWidth:f*h.from.x},t.to={height:o*h.to.y,width:e*h.to.x,outerHeight:o*h.to.y,outerWidth:e*h.to.x},h.from.y!==h.to.y&&(t.from=w.effects.setTransition(t,c,h.from.y,t.from),t.to=w.effects.setTransition(t,c,h.to.y,t.to)),h.from.x!==h.to.x&&(t.from=w.effects.setTransition(t,a,h.from.x,t.from),t.to=w.effects.setTransition(t,a,h.to.x,t.to)),t.css(t.from),t.animate(t.to,r.duration,r.easing,function(){d&&w.effects.restore(t,s)})})),n.animate(n.to,{queue:!1,duration:r.duration,easing:r.easing,complete:function(){0===n.to.opacity&&n.css("opacity",n.from.opacity),"hide"===f&&n.hide(),w.effects.restore(n,p),d||("static"===u?n.css({position:"relative",top:n.to.top,left:n.to.left}):w.each(["top","left"],function(f,t){n.css(t,function(t,o){var e=parseInt(o,10),i=f?n.to.left:n.to.top;return"auto"===o?i+"px":e+i+"px"})})),w.effects.removeWrapper(n),t()}})}});