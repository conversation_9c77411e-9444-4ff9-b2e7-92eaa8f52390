.metabox_upload_bottom{
display: inline-block;
text-decoration: none;
font-size: 13px;
line-height: 26px;
height: 215px;
margin: 0;
margin-left: 5px;
padding: 0 10px 1px;
cursor: pointer;
border-width: 1px;
border-style: solid;
-webkit-appearance: none;
-webkit-border-radius: 3px;
border-radius: 3px;
white-space: nowrap;
-webkit-box-sizing: border-box;
-moz-box-sizing: border-box;
box-sizing: border-box;
-webkit-box-shadow: inset 0 1px 0 #fff,0 1px 0 rgba(0,0,0,.08);
box-shadow: inset 0 1px 0 #fff,0 1px 0 rgba(0,0,0,.08);
background: #fafafa;
border-color: #999;
color: #222;
}

#postthumb_value_img img{width:200px;height:auto;margin-top: 10px;}

/* checkbox */
.checkbox-wrapper {
    width: 58px;
    height: 22px;
    position: relative;
    display: inline-block;
    background: #ffffff;
    border-radius: 16px;
    transition: all .3s ease-out;
    box-shadow: 0 0 0 2px #ddd;;
}
.metabox-checkbox {
    display: none!important;
}
.metaboxcheckbox-label {
    display: block;
    position: absolute;
    left: 0px;
    top: 0px;
    width: 22px;
    height: 22px;
    cursor: pointer;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0px 2px 3px rgba(0,0,0,0.2);
    transition: all .3s ease-out;
}
.metabox-checkbox:checked + .checkbox-wrapper {
    background: #0bd318;
    box-shadow: 0px 0px 0px 2px #0bd318;
}
.metabox-checkbox:checked + .checkbox-wrapper .metaboxcheckbox-label {
    left: 35px;
}