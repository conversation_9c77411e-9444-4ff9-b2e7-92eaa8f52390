/*!
 * jQuery UI Effects Drop 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/drop-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(d){return d.effects.effect.drop=function(e,t){var o=d(this),i=["position","top","bottom","left","right","opacity","height","width"],f=d.effects.setMode(o,e.mode||"hide"),n="show"===f,s=e.direction||"left",c="up"===s||"down"===s?"top":"left",p="up"===s||"left"===s?"pos":"neg",r={opacity:n?1:0};d.effects.save(o,i),o.show(),d.effects.createWrapper(o),s=e.distance||o["top"==c?"outerHeight":"outerWidth"](!0)/2,n&&o.css("opacity",0).css(c,"pos"==p?-s:s),r[c]=(n?"pos"==p?"+=":"-=":"pos"==p?"-=":"+=")+s,o.animate(r,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){"hide"===f&&o.hide(),d.effects.restore(o,i),d.effects.removeWrapper(o),t()}})}});