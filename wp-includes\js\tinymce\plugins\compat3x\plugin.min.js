!function(e){function t(){}function n(e){!o&&window&&window.console&&(o=!0,console.log("Deprecated TinyMCE API call: "+e))}function r(e,r,i,o){return e=e||this,r?(this.add=function(t,a,s){function l(n){var s=[];if("string"==typeof i&&(i=i.split(" ")),i&&"function"!=typeof i)for(var l=0;l<i.length;l++)s.push(n[i[l]]);("function"!=typeof i||(s=i(r,n,e)))&&(i||(s=[n]),s.unshift(o||e),t.apply(a||o||e,s)===!1&&n.stopImmediatePropagation())}return n("<target>.on"+r+".add(..)"),e.on(r,l,s),l},this.addToTop=function(e,t){this.add(e,t,!0)},this.remove=function(t){return e.off(r,t)},void(this.dispatch=function(){return e.fire(r),!0})):void(this.add=this.addToTop=this.remove=this.dispatch=t)}function i(i){function o(t){var n=i.settings.language||"en",r=[n,t].join("."),o=e.i18n.translate(r);return r!==o?o:e.i18n.translate(t)}function a(t,n){e.each(t.split(" "),function(e){i["on"+e]=new r(i,e,n)})}function s(e,t,n){return[t.level,n]}function l(e){return function(t,n){if(!n.selection&&!e||n.selection==e)return[n]}}function u(){function t(){return u()}var r={},i="add addMenu addSeparator collapse createMenu destroy displayColor expand focus getLength hasMenus hideMenu isActive isCollapsed isDisabled isRendered isSelected mark postRender remove removeAll renderHTML renderMenu renderNode renderTo select selectByIndex setActive setAriaProperty setColor setDisabled setSelected setState showMenu update";return n("editor.controlManager.*"),e.each(i.split(" "),function(e){r[e]=t}),r}if(!i.controlManager){i.controlManager={buttons:{},setDisabled:function(e,t){n("controlManager.setDisabled(..)"),this.buttons[e]&&this.buttons[e].disabled(t)},setActive:function(e,t){n("controlManager.setActive(..)"),this.buttons[e]&&this.buttons[e].active(t)},onAdd:new r,onPostRender:new r,add:function(e){return e},createButton:u,createColorSplitButton:u,createControl:u,createDropMenu:u,createListBox:u,createMenuButton:u,createSeparator:u,createSplitButton:u,createToolbar:u,createToolbarGroup:u,destroy:t,get:t,setControlType:u},a("PreInit BeforeRenderUI PostRender Load Init Remove Activate Deactivate","editor"),a("Click MouseUp MouseDown DblClick KeyDown KeyUp KeyPress ContextMenu Paste Submit Reset"),a("BeforeExecCommand ExecCommand","command ui value args"),a("PreProcess PostProcess LoadContent SaveContent Change"),a("BeforeSetContent BeforeGetContent SetContent GetContent",l(!1)),a("SetProgressState","state time"),a("VisualAid","element hasVisual"),a("Undo Redo",s),a("NodeChange",function(e,t){return[i.controlManager,t.element,i.selection.isCollapsed(),t]});var c=i.addButton;i.addButton=function(e,t){function n(){if(i.controlManager.buttons[e]=this,r)return r.apply(this,arguments)}var r;for(var a in t)"onpostrender"===a.toLowerCase()&&(r=t[a],t.onPostRender=n);return r||(t.onPostRender=n),t.title&&(t.title=o(t.title)),c.call(this,e,t)},i.on("init",function(){var e=i.undoManager,t=i.selection;e.onUndo=new r(i,"Undo",s,null,e),e.onRedo=new r(i,"Redo",s,null,e),e.onBeforeAdd=new r(i,"BeforeAddUndo",null,e),e.onAdd=new r(i,"AddUndo",null,e),t.onBeforeGetContent=new r(i,"BeforeGetContent",l(!0),t),t.onGetContent=new r(i,"GetContent",l(!0),t),t.onBeforeSetContent=new r(i,"BeforeSetContent",l(!0),t),t.onSetContent=new r(i,"SetContent",l(!0),t)}),i.on("BeforeRenderUI",function(){var t=i.windowManager;t.onOpen=new r,t.onClose=new r,t.createInstance=function(t,r,i,o,a,s){n("windowManager.createInstance(..)");var l=e.resolve(t);return new l(r,i,o,a,s)}})}}var o;e.util.Dispatcher=r,e.onBeforeUnload=new r(e,"BeforeUnload"),e.onAddEditor=new r(e,"AddEditor","editor"),e.onRemoveEditor=new r(e,"RemoveEditor","editor"),e.util.Cookie={get:t,getHash:t,remove:t,set:t,setHash:t},e.on("SetupEditor",i),e.PluginManager.add("compat3x",i),e.addI18n=function(t,n){var r=e.util.I18n,i=e.each;return"string"==typeof t&&t.indexOf(".")===-1?void r.add(t,n):void(e.is(t,"string")?i(n,function(e,n){r.data[t+"."+n]=e}):i(t,function(e,t){i(e,function(e,n){i(e,function(e,i){"common"===n?r.data[t+"."+i]=e:r.data[t+"."+n+"."+i]=e})})}))}}(tinymce);