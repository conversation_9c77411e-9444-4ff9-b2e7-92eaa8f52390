<div class="module-section">
	<div class="module-section-inner">
		<div class="page-width">
			<?php 
				$pid = get_option( 'index-gsjs' );
				query_posts( array( 'ignore_sticky_posts'=>true, 'p'=>$pid, 'posts_per_page'=>1, 'post_type'=>'page' ) );
				while( have_posts() ): the_post();
			?>
			<div class="module-section-title-wrapper clearfix">
				<div class="module-section-title clearfix module-icon-default">
					<h2><?php echo get_the_title( $pid ); ?></h2>
					<em>/</em>
					<h3>ABOUT</h3>
				</div>
			</div>
			<div id="a1portalSkin_ctr127364127364_mainArea" class="module-section-content">
				<div class="qhd-content">
					<div class="typo">
						<a href="<?php the_permalink(); ?>"><img class="typo_img" src="<?php echo get_option( 'index-gsjs-img' ); ?>" style="width: 391px; height: 355px;"/></a>
						<div class="typo_text">
							<h5>
							<span style="font-size:20px;"><span style="line-height: 28px;"><?php bloginfo('name'); ?></span></span></h5>
							<p>
								<span style="font-size:16px;">
								<?php if( get_option('gsjs-txt') ) { ?>
									<?php echo get_option( 'gsjs-txt' ); ?>
								<?php }else{ ?>
									<?php echo mb_strimwidth(strip_tags(apply_filters('the_content', $post->post_content)), 0, 800,"……"); ?>
								<?php }?>
								</span>
							</p>
							<p>
								<span style="font-size:16px;"><a class="btn-small btn-small-main" href="<?php the_permalink(); ?>" target="_blank" title="查看更多"><span>查看更多</span></a></span>
							</p>
						</div>
					</div>
				</div>
			</div>
			<?php endwhile; wp_reset_query(); ?>
		</div>
	</div>
</div>