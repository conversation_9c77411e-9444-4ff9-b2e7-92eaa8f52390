window.wp=window.wp||{},function(i){var e="undefined"==typeof _wpUtilSettings?{}:_wpUtilSettings;wp.template=_.memoize(function(t){var n,s={evaluate:/<#([\s\S]+?)#>/g,interpolate:/\{\{\{([\s\S]+?)\}\}\}/g,escape:/\{\{([^\}]+?)\}\}(?!\})/g,variable:"data"};return function(e){return(n=n||_.template(i("#tmpl-"+t).html(),s))(e)}}),wp.ajax={settings:e.ajax||{},post:function(e,t){return wp.ajax.send({data:_.isObject(e)?e:_.extend(t||{},{action:e})})},send:function(e,n){var t;return _.isObject(e)?n=e:(n=n||{}).data=_.extend(n.data||{},{action:e}),n=_.defaults(n||{},{type:"POST",url:wp.ajax.settings.url,context:this}),(e=(t=i.Deferred(function(t){n.success&&t.done(n.success),n.error&&t.fail(n.error),delete n.success,delete n.error,t.jqXHR=i.ajax(n).done(function(e){"1"!==e&&1!==e||(e={success:!0}),_.isObject(e)&&!_.isUndefined(e.success)?t[e.success?"resolveWith":"rejectWith"](this,[e.data]):t.rejectWith(this,[e])}).fail(function(){t.rejectWith(this,arguments)})})).promise()).abort=function(){return t.jqXHR.abort(),this},e}}}(jQuery);