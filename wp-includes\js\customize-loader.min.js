window.wp=window.wp||{},function(i){var t,o=wp.customize;i.extend(i.support,{history:!(!window.history||!history.pushState),hashchange:"onhashchange"in window&&(void 0===document.documentMode||7<document.documentMode)}),t=i.extend({},o.Events,{initialize:function(){this.body=i(document.body),t.settings&&i.support.postMessage&&(i.support.cors||!t.settings.isCrossDomain)&&(this.window=i(window),this.element=i('<div id="customize-container" />').appendTo(this.body),this.bind("open",this.overlay.show),this.bind("close",this.overlay.hide),i("#wpbody").on("click",".load-customize",function(e){e.preventDefault(),t.link=i(this),t.open(t.link.attr("href"))}),i.support.history&&this.window.on("popstate",t.popstate),i.support.hashchange&&(this.window.on("hashchange",t.hashchange),this.window.triggerHandler("hashchange")))},popstate:function(e){e=e.originalEvent.state;e&&e.customize?t.open(e.customize):t.active&&t.close()},hashchange:function(){var e=window.location.toString().split("#")[1];e&&0===e.indexOf("wp_customize=on")&&t.open(t.settings.url+"?"+e),e||i.support.history||t.close()},beforeunload:function(){if(!t.saved())return t.settings.l10n.saveAlert},open:function(e){if(!this.active){if(t.settings.browser.mobile)return window.location=e;this.originalDocumentTitle=document.title,this.active=!0,this.body.addClass("customize-loading"),this.saved=new o.Value(!0),this.iframe=i("<iframe />",{src:e,title:t.settings.l10n.mainIframeTitle}).appendTo(this.element),this.iframe.one("load",this.loaded),this.messenger=new o.Messenger({url:e,channel:"loader",targetWindow:this.iframe[0].contentWindow}),history.replaceState&&this.messenger.bind("changeset-uuid",function(e){var t=document.createElement("a");t.href=location.href,t.search=i.param(_.extend(o.utils.parseQueryString(t.search.substr(1)),{changeset_uuid:e})),history.replaceState({customize:t.href},"",t.href)}),this.messenger.bind("ready",function(){t.messenger.send("back")}),this.messenger.bind("close",function(){i.support.history?history.back():i.support.hashchange?window.location.hash="":t.close()}),i(window).on("beforeunload",this.beforeunload),this.messenger.bind("saved",function(){t.saved(!0)}),this.messenger.bind("change",function(){t.saved(!1)}),this.messenger.bind("title",function(e){window.document.title=e}),this.pushState(e),this.trigger("open")}},pushState:function(e){var t=e.split("?")[1];i.support.history&&window.location.href!==e?history.pushState({customize:e},"",e):!i.support.history&&i.support.hashchange&&t&&(window.location.hash="wp_customize=on&"+t),this.trigger("open")},opened:function(){t.body.addClass("customize-active full-overlay-active").attr("aria-busy","true")},close:function(){this.active&&(this.saved()||confirm(t.settings.l10n.saveAlert)?(this.active=!1,this.trigger("close"),this.originalDocumentTitle&&(document.title=this.originalDocumentTitle)):history.forward())},closed:function(){t.iframe.remove(),t.messenger.destroy(),t.iframe=null,t.messenger=null,t.saved=null,t.body.removeClass("customize-active full-overlay-active").removeClass("customize-loading"),i(window).off("beforeunload",t.beforeunload),t.link&&t.link.focus()},loaded:function(){t.body.removeClass("customize-loading").attr("aria-busy","false")},overlay:{show:function(){this.element.fadeIn(200,t.opened)},hide:function(){this.element.fadeOut(200,t.closed)}}}),i(function(){t.settings=_wpCustomizeLoaderSettings,t.initialize()}),o.Loader=t}((wp,jQuery));