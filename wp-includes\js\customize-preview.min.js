!function(o){var s,i,r,a,c,n,u,l,d,p=wp.customize,h={};(i=history).replaceState&&(c=function(e){var t,n=document.createElement("a");return n.href=e,t=p.utils.parseQueryString(location.search.substr(1)),(e=p.utils.parseQueryString(n.search.substr(1))).customize_changeset_uuid=t.customize_changeset_uuid,t.customize_theme&&(e.customize_theme=t.customize_theme),t.customize_messenger_channel&&(e.customize_messenger_channel=t.customize_messenger_channel),n.search=o.param(e),n.href},i.replaceState=(r=i.replaceState,function(e,t,n){return h=e,r.call(i,e,t,"string"==typeof n&&0<n.length?c(n):n)}),i.pushState=(a=i.pushState,function(e,t,n){return h=e,a.call(i,e,t,"string"==typeof n&&0<n.length?c(n):n)}),window.addEventListener("popstate",function(e){h=e.state})),s=function(t,n,i){var s;return function(){var e=arguments;i=i||this,clearTimeout(s),s=setTimeout(function(){s=null,t.apply(i,e)},n)}},p.Preview=p.Messenger.extend({initialize:function(e,t){var n=this,i=document.createElement("a");p.Messenger.prototype.initialize.call(n,e,t),i.href=n.origin(),n.add("scheme",i.protocol.replace(/:$/,"")),n.body=o(document.body),n.window=o(window),p.settings.channel&&(n.body.on("click.preview","a",function(e){n.handleLinkClick(e)}),n.body.on("submit.preview","form",function(e){n.handleFormSubmit(e)}),n.window.on("scroll.preview",s(function(){n.send("scroll",n.window.scrollTop())},200)),n.bind("scroll",function(e){n.window.scrollTop(e)}))},handleLinkClick:function(e){var t=o(e.target).closest("a");if(!_.isUndefined(t.attr("href"))&&!("#"===t.attr("href").substr(0,1))&&/^https?:$/.test(t.prop("protocol"))){if(!p.isLinkPreviewable(t[0]))return wp.a11y.speak(p.settings.l10n.linkUnpreviewable),void e.preventDefault();e.preventDefault(),e.shiftKey||this.send("url",t.prop("href"))}},handleFormSubmit:function(e){var t=document.createElement("a"),n=o(e.target);if(t.href=n.prop("action"),"GET"!==n.prop("method").toUpperCase()||!p.isLinkPreviewable(t))return wp.a11y.speak(p.settings.l10n.formUnpreviewable),void e.preventDefault();e.isDefaultPrevented()||(1<t.search.length&&(t.search+="&"),t.search+=n.serialize(),this.send("url",t.href)),e.preventDefault()}}),p.addLinkPreviewing=function(){var t="a[href], area";o(document.body).find(t).each(function(){p.prepareLinkPreview(this)}),"undefined"!=typeof MutationObserver?(p.mutationObserver=new MutationObserver(function(e){_.each(e,function(e){o(e.target).find(t).each(function(){p.prepareLinkPreview(this)})})}),p.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0})):o(document.documentElement).on("click focus mouseover",t,function(){p.prepareLinkPreview(this)})},p.isLinkPreviewable=function(t,e){var n,i,e=_.extend({},{allowAdminAjax:!1},e||{});return"javascript:"===t.protocol||("https:"===t.protocol||"http:"===t.protocol)&&(i=t.host.replace(/:(80|443)$/,""),n=document.createElement("a"),!_.isUndefined(_.find(p.settings.url.allowed,function(e){return n.href=e,n.protocol===t.protocol&&n.host.replace(/:(80|443)$/,"")===i&&0===t.pathname.indexOf(n.pathname.replace(/\/$/,""))}))&&(!/\/wp-(login|signup)\.php$/.test(t.pathname)&&(/\/wp-admin\/admin-ajax\.php$/.test(t.pathname)?e.allowAdminAjax:!/\/wp-(admin|includes|content)(\/|$)/.test(t.pathname))))},p.prepareLinkPreview=function(e){var t;o(e).closest("#wpadminbar").length||"#"!==o(e).attr("href").substr(0,1)&&/^https?:$/.test(e.protocol)&&(p.settings.channel&&"https"===p.preview.scheme.get()&&"http:"===e.protocol&&-1!==p.settings.url.allowedHosts.indexOf(e.host)&&(e.protocol="https:"),p.isLinkPreviewable(e)?(o(e).removeClass("customize-unpreviewable"),(t=p.utils.parseQueryString(e.search.substring(1))).customize_changeset_uuid=p.settings.changeset.uuid,p.settings.theme.active||(t.customize_theme=p.settings.theme.stylesheet),p.settings.channel&&(t.customize_messenger_channel=p.settings.channel),e.search=o.param(t),p.settings.channel&&(e.target="_self")):p.settings.channel&&o(e).addClass("customize-unpreviewable"))},p.addRequestPreviewing=function(){o.ajaxPrefilter(function(e,t,n){var i,s,r={},a=document.createElement("a");a.href=e.url,p.isLinkPreviewable(a,{allowAdminAjax:!0})&&(i=p.utils.parseQueryString(a.search.substring(1)),p.each(function(e){e._dirty&&(r[e.id]=e.get())}),_.isEmpty(r)||("POST"!==(s=e.type.toUpperCase())&&(n.setRequestHeader("X-HTTP-Method-Override",s),i._method=s,e.type="POST"),e.data?e.data+="&":e.data="",e.data+=o.param({customized:JSON.stringify(r)})),i.customize_changeset_uuid=p.settings.changeset.uuid,p.settings.theme.active||(i.customize_theme=p.settings.theme.stylesheet),a.search=o.param(i),e.url=a.href)})},p.addFormPreviewing=function(){o(document.body).find("form").each(function(){p.prepareFormPreview(this)}),"undefined"!=typeof MutationObserver&&(p.mutationObserver=new MutationObserver(function(e){_.each(e,function(e){o(e.target).find("form").each(function(){p.prepareFormPreview(this)})})}),p.mutationObserver.observe(document.documentElement,{childList:!0,subtree:!0}))},p.prepareFormPreview=function(i){var e,t={};i.action||(i.action=location.href),(e=document.createElement("a")).href=i.action,p.settings.channel&&"https"===p.preview.scheme.get()&&"http:"===e.protocol&&-1!==p.settings.url.allowedHosts.indexOf(e.host)&&(e.protocol="https:",i.action=e.href),"GET"===i.method.toUpperCase()&&p.isLinkPreviewable(e)?(o(i).removeClass("customize-unpreviewable"),t.customize_changeset_uuid=p.settings.changeset.uuid,p.settings.theme.active||(t.customize_theme=p.settings.theme.stylesheet),p.settings.channel&&(t.customize_messenger_channel=p.settings.channel),_.each(t,function(e,t){var n=o(i).find('input[name="'+t+'"]');n.length?n.val(e):o(i).prepend(o("<input>",{type:"hidden",name:t,value:e}))}),p.settings.channel&&(i.target="_self")):p.settings.channel&&o(i).addClass("customize-unpreviewable")},p.keepAliveCurrentUrl=(n=location.pathname,u=location.search.substr(1),l=null,d=["customize_theme","customize_changeset_uuid","customize_messenger_channel"],function(){var e,t;u!==location.search.substr(1)||n!==location.pathname?(e=document.createElement("a"),null===l&&(e.search=u,l=p.utils.parseQueryString(u),_.each(d,function(e){delete l[e]})),e.href=location.href,t=p.utils.parseQueryString(e.search.substr(1)),_.each(d,function(e){delete t[e]}),n===location.pathname&&_.isEqual(l,t)?p.preview.send("keep-alive"):(e.search=o.param(t),e.hash="",p.settings.url.self=e.href,p.preview.send("ready",{currentUrl:p.settings.url.self,activePanels:p.settings.activePanels,activeSections:p.settings.activeSections,activeControls:p.settings.activeControls,settingValidities:p.settings.settingValidities})),l=t,u=location.search.substr(1),n=location.pathname):p.preview.send("keep-alive")}),p.settingPreviewHandlers={custom_logo:function(e){o("body").toggleClass("wp-custom-logo",!!e)},custom_css:function(e){o("#wp-custom-css").text(e)},background:function(){var e="",t={};_.each(["color","image","preset","position_x","position_y","size","repeat","attachment"],function(e){t[e]=p("background_"+e)}),o(document.body).toggleClass("custom-background",!(!t.color()&&!t.image())),t.color()&&(e+="background-color: "+t.color()+";"),t.image()&&(e+='background-image: url("'+t.image()+'");',e+="background-size: "+t.size()+";",e+="background-position: "+t.position_x()+" "+t.position_y()+";",e+="background-repeat: "+t.repeat()+";",e+="background-attachment: "+t.attachment()+";"),o("#custom-background-css").text("body.custom-background { "+e+" }")}},o(function(){var e,t;p.settings=window._wpCustomizeSettings,p.settings&&(p.preview=new p.Preview({url:window.location.href,channel:p.settings.channel}),p.addLinkPreviewing(),p.addRequestPreviewing(),p.addFormPreviewing(),t=function(e,t,n){var i=p(e);i?i.set(t):(n=n||!1,i=p.create(e,t,{id:e}),n&&(i._dirty=!0))},p.preview.bind("settings",function(e){o.each(e,t)}),p.preview.trigger("settings",p.settings.values),o.each(p.settings._dirty,function(e,t){t=p(t);t&&(t._dirty=!0)}),p.preview.bind("setting",function(e){t.apply(null,e.concat(!0))}),p.preview.bind("sync",function(t){t.settings&&t["settings-modified-while-loading"]&&_.each(_.keys(t.settings),function(e){p.has(e)&&!t["settings-modified-while-loading"][e]&&delete t.settings[e]}),o.each(t,function(e,t){p.preview.trigger(e,t)}),p.preview.send("synced")}),p.preview.bind("active",function(){p.preview.send("nonce",p.settings.nonce),p.preview.send("documentTitle",document.title),p.preview.send("scroll",o(window).scrollTop())}),p.preview.bind("saved",function(e){e.next_changeset_uuid&&(p.settings.changeset.uuid=e.next_changeset_uuid,o(document.body).find("a[href], area").each(function(){p.prepareLinkPreview(this)}),o(document.body).find("form").each(function(){p.prepareFormPreview(this)}),history.replaceState&&history.replaceState(h,"",location.href)),p.trigger("saved",e)}),p.preview.bind("changeset-saved",function(e){_.each(e.saved_changeset_values,function(e,t){t=p(t);t&&_.isEqual(t.get(),e)&&(t._dirty=!1)})}),p.preview.bind("nonce-refresh",function(e){o.extend(p.settings.nonce,e)}),p.preview.send("ready",{currentUrl:p.settings.url.self,activePanels:p.settings.activePanels,activeSections:p.settings.activeSections,activeControls:p.settings.activeControls,settingValidities:p.settings.settingValidities}),setInterval(p.keepAliveCurrentUrl,p.settings.timeouts.keepAliveSend),p.preview.bind("loading-initiated",function(){o("body").addClass("wp-customizer-unloading")}),p.preview.bind("loading-failed",function(){o("body").removeClass("wp-customizer-unloading")}),e=o.map(["color","image","preset","position_x","position_y","size","repeat","attachment"],function(e){return"background_"+e}),p.when.apply(p,e).done(function(){o.each(arguments,function(){this.bind(p.settingPreviewHandlers.background)})}),p("custom_logo",function(e){p.settingPreviewHandlers.custom_logo.call(e,e.get()),e.bind(p.settingPreviewHandlers.custom_logo)}),p("custom_css["+p.settings.theme.stylesheet+"]",function(e){e.bind(p.settingPreviewHandlers.custom_css)}),p.trigger("preview-ready"))})}((wp,jQuery));