/*!
 * jQuery UI Effects Fade 1.11.4
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/fade-effect/
 */
!function(e){"function"==typeof define&&define.amd?define(["jquery","./effect"],e):e(jQuery)}(function(i){return i.effects.effect.fade=function(e,t){var f=i(this),n=i.effects.setMode(f,e.mode||"toggle");f.animate({opacity:n},{queue:!1,duration:e.duration,easing:e.easing,complete:t})}});