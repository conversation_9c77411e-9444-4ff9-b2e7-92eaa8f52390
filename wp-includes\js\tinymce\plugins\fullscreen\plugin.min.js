tinymce.PluginManager.add("fullscreen",function(e){function t(){var e,t,n=window,r=document,i=r.body;return i.offsetWidth&&(e=i.offsetWidth,t=i.offsetHeight),n.innerWidth&&n.innerHeight&&(e=n.innerWidth,t=n.innerHeight),{w:e,h:t}}function n(){var e=tinymce.DOM.getViewPort();return{x:e.x,y:e.y}}function r(e){scrollTo(e.x,e.y)}function i(){function i(){f.setStyle(m,"height",t().h-(h.clientHeight-m.clientHeight))}var p,h,m,g,v=document.body,y=document.documentElement;d=!d,h=e.getContainer(),p=h.style,m=e.getContentAreaContainer().firstChild,g=m.style,d?(c=n(),o=g.width,a=g.height,g.width=g.height="100%",l=p.width,u=p.height,p.width=p.height="",f.addClass(v,"mce-fullscreen"),f.addClass(y,"mce-fullscreen"),f.addClass(h,"mce-fullscreen"),f.bind(window,"resize",i),i(),s=i):(g.width=o,g.height=a,l&&(p.width=l),u&&(p.height=u),f.removeClass(v,"mce-fullscreen"),f.removeClass(y,"mce-fullscreen"),f.removeClass(h,"mce-fullscreen"),f.unbind(window,"resize",s),r(c)),e.fire("FullscreenStateChanged",{state:d})}var o,a,s,l,u,c,d=!1,f=tinymce.DOM;if(!e.settings.inline)return e.on("init",function(){e.addShortcut("Ctrl+Shift+F","",i)}),e.on("remove",function(){s&&f.unbind(window,"resize",s)}),e.addCommand("mceFullScreen",i),e.addMenuItem("fullscreen",{text:"Fullscreen",shortcut:"Ctrl+Shift+F",selectable:!0,onClick:function(){i(),e.focus()},onPostRender:function(){var t=this;e.on("FullscreenStateChanged",function(e){t.active(e.state)})},context:"view"}),e.addButton("fullscreen",{tooltip:"Fullscreen",shortcut:"Ctrl+Shift+F",onClick:i,onPostRender:function(){var t=this;e.on("FullscreenStateChanged",function(e){t.active(e.state)})}}),{isFullscreen:function(){return d}}});