!function(e){function t(){return e("<div/>")}var o=Math.abs,i=Math.max,s=Math.min,n=Math.round;e.imgAreaSelect=function(r,c){function d(e){return e+gt.left-vt.left}function a(e){return e+gt.top-vt.top}function u(e){return e-gt.left+vt.left}function l(e){return e-gt.top+vt.top}function h(e){return i(e.pageX||0,m(e).x)-vt.left}function f(e){return i(e.pageY||0,m(e).y)-vt.top}function m(e){var t=e.originalEvent||{};return t.touches&&t.touches.length?{x:t.touches[0].pageX,y:t.touches[0].pageY}:{x:0,y:0}}function p(e){var t=e||B,o=e||Q;return{x1:n(wt.x1*t),y1:n(wt.y1*o),x2:n(wt.x2*t),y2:n(wt.y2*o),width:n(wt.x2*t)-n(wt.x1*t),height:n(wt.y2*o)-n(wt.y1*o)}}function y(e,t,o,i,s){var r=s||B,c=s||Q;wt={x1:n(e/r||0),y1:n(t/c||0),x2:n(o/r||0),y2:n(i/c||0)},wt.width=wt.x2-wt.x1,wt.height=wt.y2-wt.y1}function g(){T&&lt.width()&&(gt={left:n(lt.offset().left),top:n(lt.offset().top)},R=lt.innerWidth(),X=lt.innerHeight(),gt.top+=lt.outerHeight()-X>>1,gt.left+=lt.outerWidth()-R>>1,G=n(c.minWidth/B)||0,J=n(c.minHeight/Q)||0,U=n(s(c.maxWidth/B||1<<24,R)),V=n(s(c.maxHeight/Q||1<<24,X)),"1.3.2"!=e().jquery||"fixed"!=xt||St.getBoundingClientRect||(gt.top+=i(document.body.scrollTop,St.scrollTop),gt.left+=i(document.body.scrollLeft,St.scrollLeft)),vt=/absolute|relative/.test(Y.css("position"))?{left:n(Y.offset().left)-Y.scrollLeft(),top:n(Y.offset().top)-Y.scrollTop()}:"fixed"==xt?{left:e(document).scrollLeft(),top:e(document).scrollTop()}:{left:0,top:0},j=d(0),D=a(0),(wt.x2>R||wt.y2>X)&&C())}function v(t){if(_){switch(ht.css({left:d(wt.x1),top:a(wt.y1)}).add(ft).width(dt=wt.width).height(at=wt.height),ft.add(mt).add(yt).css({left:0,top:0}),mt.width(i(dt-mt.outerWidth()+mt.innerWidth(),0)).height(i(at-mt.outerHeight()+mt.innerHeight(),0)),e(pt[0]).css({left:j,top:D,width:wt.x1,height:X}),e(pt[1]).css({left:j+wt.x1,top:D,width:dt,height:wt.y1}),e(pt[2]).css({left:j+wt.x2,top:D,width:R-wt.x2,height:X}),e(pt[3]).css({left:j+wt.x1,top:D+wt.y2,width:dt,height:X-wt.y2}),dt-=yt.outerWidth(),at-=yt.outerHeight(),yt.length){case 8:e(yt[4]).css({left:dt>>1}),e(yt[5]).css({left:dt,top:at>>1}),e(yt[6]).css({left:dt>>1,top:at}),e(yt[7]).css({top:at>>1});case 4:yt.slice(1,3).css({left:dt}),yt.slice(2,4).css({top:at})}t!==!1&&(e.imgAreaSelect.onKeyPress!=kt&&e(document).unbind(e.imgAreaSelect.keyPress,e.imgAreaSelect.onKeyPress),c.keys&&e(document)[e.imgAreaSelect.keyPress](e.imgAreaSelect.onKeyPress=kt)),Ct&&mt.outerWidth()-mt.innerWidth()==2&&(mt.css("margin",0),setTimeout(function(){mt.css("margin","auto")},0))}}function b(e){g(),v(e),et=d(wt.x1),tt=a(wt.y1),ot=d(wt.x2),it=a(wt.y2)}function x(e,t){c.fadeSpeed?e.fadeOut(c.fadeSpeed,t):e.hide()}function w(e){var t=u(h(e))-wt.x1,o=l(f(e))-wt.y1;ut||(g(),ut=!0,ht.one("mouseout",function(){ut=!1})),F="",c.resizable&&(o<=c.resizeMargin?F="n":o>=wt.height-c.resizeMargin&&(F="s"),t<=c.resizeMargin?F+="w":t>=wt.width-c.resizeMargin&&(F+="e")),ht.css("cursor",F?F+"-resize":c.movable?"move":""),L&&L.toggle()}function S(){e("body").css("cursor",""),(c.autoHide||wt.width*wt.height==0)&&x(ht.add(pt),function(){e(this).hide()}),e(document).off("mousemove touchmove",A),ht.on("mousemove touchmove",w),c.onSelectEnd(r,p())}function z(t){return"mousedown"==t.type&&1!=t.which?!1:(w(t),g(),F?(e("body").css("cursor",F+"-resize"),et=d(wt[/w/.test(F)?"x2":"x1"]),tt=a(wt[/n/.test(F)?"y2":"y1"]),e(document).on("mousemove touchmove",A).one("mouseup touchend",S),ht.off("mousemove touchmove",w)):c.movable?($=j+wt.x1-h(t),q=D+wt.y1-f(t),ht.off("mousemove touchmove",w),e(document).on("mousemove touchmove",I).one("mouseup touchend",function(){c.onSelectEnd(r,p()),e(document).off("mousemove touchmove",I),ht.on("mousemove touchmove",w)})):lt.mousedown(t),!1)}function k(e){Z&&(e?(ot=i(j,s(j+R,et+o(it-tt)*Z*(ot>et||-1))),it=n(i(D,s(D+X,tt+o(ot-et)/Z*(it>tt||-1)))),ot=n(ot)):(it=i(D,s(D+X,tt+o(ot-et)/Z*(it>tt||-1))),ot=n(i(j,s(j+R,et+o(it-tt)*Z*(ot>et||-1)))),it=n(it)))}function C(){et=s(et,j+R),tt=s(tt,D+X),o(ot-et)<G&&(ot=et-G*(et>ot||-1),j>ot?et=j+G:ot>j+R&&(et=j+R-G)),o(it-tt)<J&&(it=tt-J*(tt>it||-1),D>it?tt=D+J:it>D+X&&(tt=D+X-J)),ot=i(j,s(ot,j+R)),it=i(D,s(it,D+X)),k(o(ot-et)<o(it-tt)*Z),o(ot-et)>U&&(ot=et-U*(et>ot||-1),k()),o(it-tt)>V&&(it=tt-V*(tt>it||-1),k(!0)),wt={x1:u(s(et,ot)),x2:u(i(et,ot)),y1:l(s(tt,it)),y2:l(i(tt,it)),width:o(ot-et),height:o(it-tt)},v(),c.onSelectChange(r,p())}function A(e){return ot=/w|e|^$/.test(F)||Z?h(e):d(wt.x2),it=/n|s|^$/.test(F)||Z?f(e):a(wt.y2),C(),!1}function W(t,o){ot=(et=t)+wt.width,it=(tt=o)+wt.height,e.extend(wt,{x1:u(et),y1:l(tt),x2:u(ot),y2:l(it)}),v(),c.onSelectChange(r,p())}function I(e){return et=i(j,s($+h(e),j+R-wt.width)),tt=i(D,s(q+f(e),D+X-wt.height)),W(et,tt),e.preventDefault(),!1}function K(){e(document).off("mousemove touchmove",K),g(),ot=et,it=tt,C(),F="",pt.is(":visible")||ht.add(pt).hide().fadeIn(c.fadeSpeed||0),_=!0,e(document).off("mouseup touchend",P).on("mousemove touchmove",A).one("mouseup touchend",S),ht.off("mousemove touchmove",w),c.onSelectStart(r,p())}function P(){e(document).off("mousemove touchmove",K).off("mouseup touchend",P),x(ht.add(pt)),y(u(et),l(tt),u(et),l(tt)),this instanceof e.imgAreaSelect||(c.onSelectChange(r,p()),c.onSelectEnd(r,p()))}function N(t){return 1!=t.which||pt.is(":animated")?!1:(g(),$=et=h(t),q=tt=f(t),e(document).on({"mousemove touchmove":K,"mouseup touchend":P}),!1)}function H(){b(!1)}function M(){T=!0,O(c=e.extend({classPrefix:"imgareaselect",movable:!0,parent:"body",resizable:!0,resizeMargin:10,onInit:function(){},onSelectStart:function(){},onSelectChange:function(){},onSelectEnd:function(){}},c)),ht.add(pt).css({visibility:""}),c.show&&(_=!0,g(),v(),ht.add(pt).hide().fadeIn(c.fadeSpeed||0)),setTimeout(function(){c.onInit(r,p())},0)}function E(e,t){for(var o in t)void 0!==c[o]&&e.css(t[o],c[o])}function O(o){if(o.parent&&(Y=e(o.parent)).append(ht.add(pt)),e.extend(c,o),g(),null!=o.handles){for(yt.remove(),yt=e([]),rt=o.handles?"corners"==o.handles?4:8:0;rt--;)yt=yt.add(t());yt.addClass(c.classPrefix+"-handle").css({position:"absolute",fontSize:0,zIndex:bt+1||1}),!parseInt(yt.css("width"))>=0&&yt.width(5).height(5),(ct=c.borderWidth)&&yt.css({borderWidth:ct,borderStyle:"solid"}),E(yt,{borderColor1:"border-color",borderColor2:"background-color",borderOpacity:"opacity"})}for(B=c.imageWidth/R||1,Q=c.imageHeight/X||1,null!=o.x1&&(y(o.x1,o.y1,o.x2,o.y2),o.show=!o.hide),o.keys&&(c.keys=e.extend({shift:1,ctrl:"resize"},o.keys)),pt.addClass(c.classPrefix+"-outer"),ft.addClass(c.classPrefix+"-selection"),rt=0;rt++<4;)e(mt[rt-1]).addClass(c.classPrefix+"-border"+rt);E(ft,{selectionColor:"background-color",selectionOpacity:"opacity"}),E(mt,{borderOpacity:"opacity",borderWidth:"border-width"}),E(pt,{outerColor:"background-color",outerOpacity:"opacity"}),(ct=c.borderColor1)&&e(mt[0]).css({borderStyle:"solid",borderColor:ct}),(ct=c.borderColor2)&&e(mt[1]).css({borderStyle:"dashed",borderColor:ct}),ht.append(ft.add(mt).add(L)).append(yt),Ct&&((ct=(pt.css("filter")||"").match(/opacity=(\d+)/))&&pt.css("opacity",ct[1]/100),(ct=(mt.css("filter")||"").match(/opacity=(\d+)/))&&mt.css("opacity",ct[1]/100)),o.hide?x(ht.add(pt)):o.show&&T&&(_=!0,ht.add(pt).fadeIn(c.fadeSpeed||0),b()),Z=(nt=(c.aspectRatio||"").split(/:/))[0]/nt[1],lt.add(pt).unbind("mousedown",N),c.disable||c.enable===!1?(ht.off({"mousemove touchmove":w,"mousedown touchstart":z}),e(window).off("resize",H)):((c.enable||c.disable===!1)&&((c.resizable||c.movable)&&ht.on({"mousemove touchmove":w,"mousedown touchstart":z}),e(window).resize(H)),c.persistent||lt.add(pt).on("mousedown touchstart",N)),c.enable=c.disable=void 0}var T,L,j,D,R,X,Y,$,q,B,Q,F,G,J,U,V,Z,_,et,tt,ot,it,st,nt,rt,ct,dt,at,ut,lt=e(r),ht=t(),ft=t(),mt=t().add(t()).add(t()).add(t()),pt=t().add(t()).add(t()).add(t()),yt=e([]),gt={left:0,top:0},vt={left:0,top:0},bt=0,xt="absolute",wt={x1:0,y1:0,x2:0,y2:0,width:0,height:0},St=document.documentElement,zt=navigator.userAgent,kt=function(e){var t,o,n=c.keys,r=e.keyCode;if(t=isNaN(n.alt)||!e.altKey&&!e.originalEvent.altKey?!isNaN(n.ctrl)&&e.ctrlKey?n.ctrl:!isNaN(n.shift)&&e.shiftKey?n.shift:isNaN(n.arrows)?10:n.arrows:n.alt,"resize"==n.arrows||"resize"==n.shift&&e.shiftKey||"resize"==n.ctrl&&e.ctrlKey||"resize"==n.alt&&(e.altKey||e.originalEvent.altKey)){switch(r){case 37:t=-t;case 39:o=i(et,ot),et=s(et,ot),ot=i(o+t,et),k();break;case 38:t=-t;case 40:o=i(tt,it),tt=s(tt,it),it=i(o+t,tt),k(!0);break;default:return}C()}else switch(et=s(et,ot),tt=s(tt,it),r){case 37:W(i(et-t,j),tt);break;case 38:W(et,i(tt-t,D));break;case 39:W(et+s(t,R-u(ot)),tt);break;case 40:W(et,tt+s(t,X-l(it)));break;default:return}return!1};this.remove=function(){O({disable:!0}),ht.add(pt).remove()},this.getOptions=function(){return c},this.setOptions=O,this.getSelection=p,this.setSelection=y,this.cancelSelection=P,this.update=b;var Ct=(/msie ([\w.]+)/i.exec(zt)||[])[1],At=/opera/i.test(zt),Wt=/webkit/i.test(zt)&&!/chrome/i.test(zt);for(st=lt;st.length;)bt=i(bt,isNaN(st.css("z-index"))?bt:st.css("z-index")),"fixed"==st.css("position")&&(xt="fixed"),st=st.parent(":not(body)");bt=c.zIndex||bt,Ct&&lt.attr("unselectable","on"),e.imgAreaSelect.keyPress=Ct||Wt?"keydown":"keypress",At&&(L=t().css({width:"100%",height:"100%",position:"absolute",zIndex:bt+2||2})),ht.add(pt).css({visibility:"hidden",position:xt,overflow:"hidden",zIndex:bt||"0"}),ht.css({zIndex:bt+2||2}),ft.add(mt).css({position:"absolute",fontSize:0}),r.complete||"complete"==r.readyState||!lt.is("img")?M():lt.one("load",M),!T&&Ct&&Ct>=7&&(r.src=r.src)},e.fn.imgAreaSelect=function(t){return t=t||{},this.each(function(){e(this).data("imgAreaSelect")?t.remove?(e(this).data("imgAreaSelect").remove(),e(this).removeData("imgAreaSelect")):e(this).data("imgAreaSelect").setOptions(t):t.remove||(void 0===t.enable&&void 0===t.disable&&(t.enable=!0),e(this).data("imgAreaSelect",new e.imgAreaSelect(this,t)))}),t.instance?e(this).data("imgAreaSelect"):this}}(jQuery);